package com.stt.android.glance

import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.LocalSize
import androidx.glance.action.Action
import androidx.glance.action.clickable
import androidx.glance.appwidget.appWidgetBackground
import androidx.glance.appwidget.cornerRadius
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.padding
import androidx.glance.layout.size
import com.stt.android.R
import com.stt.android.glance.ext.getDimensionValue

@Composable
fun HomeWidgetSurface(
    targetWidth: Float,
    targetHeight: Float,
    paddingHorizontal: Float,
    paddingVertical: Float,
    modifier: GlanceModifier = GlanceModifier,
    maxWidth: Float? = null,
    maxHeight: Float? = null,
    onClick: Action? = null,
    content: @Composable (Dp, Dp) -> Unit,
) {
    val context = LocalContext.current
    val size = LocalSize.current

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        // In some cases, the size of the widget is zero, for example, widget picker in the launcher.
        val rawWidgetWidth = size.width.takeIf { it.value > 0 } ?: targetWidth.dp
        val rawWidgetHeight = size.height.takeIf { it.value > 0 } ?: targetHeight.dp
        val widgetWidth = maxWidth?.let { rawWidgetWidth.coerceAtMost(it.dp) } ?: rawWidgetWidth
        val widgetHeight = maxHeight?.let { rawWidgetHeight.coerceAtMost(it.dp) } ?: rawWidgetHeight
        val widthRatio = widgetWidth.value / targetWidth
        val heightRatio = widgetHeight.value / targetHeight
        val scale = widthRatio.coerceAtMost(heightRatio)

        CompositionLocalProvider(LocalHomeWidgetScale provides scale) {
            val cornerRad = context.getDimensionValue(R.dimen.systemwidget_background_corner_radius)
            Box(
                modifier = GlanceModifier
                    .cornerRadius(cornerRad.dp) // Only works on Android S+
                    .then(onClick?.let { GlanceModifier.clickable(onClick = it) } ?: GlanceModifier)
                    .then(
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                            GlanceModifier.background(GlanceTheme.colors.surface)
                        } else {
                            GlanceModifier.background(ImageProvider(R.drawable.home_widget_background))
                        }
                    )
                    .size(targetWidth.scaledDp, targetHeight.scaledDp)
                    .padding(
                        horizontal = paddingHorizontal.scaledDp,
                        vertical = paddingVertical.scaledDp,
                    )
                    .appWidgetBackground(),
                content = {
                    content(
                        (targetWidth - 2 * paddingHorizontal).scaledDp,
                        (targetHeight - 2 * paddingVertical).scaledDp,
                    )
                },
            )
        }
    }
}
