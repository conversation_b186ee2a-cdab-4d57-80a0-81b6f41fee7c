package com.stt.android.glance

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.text.TextPaint
import androidx.annotation.FontRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.applyCanvas
import androidx.core.graphics.createBitmap
import androidx.core.util.TypedValueCompat
import androidx.glance.ColorFilter
import androidx.glance.GlanceModifier
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.layout.ContentScale
import androidx.glance.text.FontWeight
import androidx.glance.text.Text
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.stt.android.FontRefs
import com.stt.android.utils.BrandUtils
import kotlin.math.roundToInt

private const val WORD_BREAK = " "

private fun String.asBitmap(
    width: Int,
    font: Typeface?,
    fontSize: Float,
    color: Int,
    lineSpacingMultiplier: Float,
): Bitmap {
    if (this.isBlank()) {
        return createBitmap(1, 1)
    }

    val paint = TextPaint(Paint.ANTI_ALIAS_FLAG).apply {
        this.textSize = fontSize
        this.color = color
        this.typeface = font
    }
    val lineWidth = width.takeIf { it > 0 } ?: paint.measureText(this).roundToInt()
    val clips = this.wrapText(lineWidth, paint) { lineWidth, paint ->
        clipText(lineWidth, paint)
    }
    val baseline = -paint.ascent()
    val rawLineHeight = baseline + paint.descent()
    val lineHeight = rawLineHeight * lineSpacingMultiplier
    val lineHeightOffset = (lineHeight - rawLineHeight) / 2f
    return createBitmap(lineWidth, (lineHeight * clips.size).roundToInt()).applyCanvas {
        clips.forEachIndexed<String> { index, subtext ->
            drawText(subtext, 0f, baseline + index * lineHeight + lineHeightOffset, paint)
        }
    }
}

private fun String.wrapText(
    lineWidth: Int,
    paint: TextPaint,
    clipText: String.(Int, TextPaint) -> Int,
): List<String> {
    return buildList {
        var sum = 0
        do {
            val rawOffset = substring(sum).clipText(lineWidth, paint)
            val offset = wrapWord(sum, rawOffset)
            add(substring(sum, sum + offset))
            sum += offset
        } while (sum < length)
    }
}

private fun String.wrapWord(startIndex: Int, offset: Int): Int {
    val endIndex = startIndex + offset
    val line = substring(startIndex, endIndex)
    if (line.endsWith(WORD_BREAK) || endIndex == length) {
        return offset
    }

    val next = substring(endIndex, endIndex + 1)
    if (next == WORD_BREAK) {
        return offset + 1
    }

    val index = line.lastIndexOf(WORD_BREAK)
    return if (index != -1) {
        index + 1
    } else offset
}

private fun String.clipText(lineWidth: Int, paint: TextPaint): Int {
    val fullWidth = paint.measureText(this).roundToInt()
    if (fullWidth <= lineWidth) return length

    val singleWidth = paint.measureText(substring(0, 1)).roundToInt()
    if (lineWidth <= singleWidth) return 1

    (1..<length).forEach { index ->
        val currWidth = paint.measureText(substring(0, index)).roundToInt()
        val nextWidth = paint.measureText(substring(0, index + 1)).roundToInt()
        if (lineWidth in currWidth..<nextWidth) {
            return index
        }
    }
    return 0
}

/**
 * WidgetText renders a text string as an image. Use Dp for fontSize to respect the design.
 *
 * Note: for SizeMode.Exact widgets, the total bitmap count is limited to 16 on huawei devices
 * for both landscape and portrait. Each widget can hold at most 8 bitmaps.
 * Or the later bitmaps will override other bitmaps from the beginning.
 *
 * In order to avoid this limitation, use the default Text composable for huawei devices.
 * And no custom font is supported on huawei devices.
 */
@SuppressLint("RestrictedApi")
@Composable
fun WidgetText(
    text: String,
    @FontRes fontRes: Int,
    fontSize: Dp,
    color: ColorProvider,
    modifier: GlanceModifier = GlanceModifier,
    availableWidth: Dp = 0.dp,
    lineSpacingMultiplier: Float = 1.2f,
) {
    val context = LocalContext.current
    if (BrandUtils.isHuawei()) {
        val fontSizePx = TypedValueCompat.dpToPx(fontSize.value, context.resources.displayMetrics)
        val fontSizeSp = TypedValueCompat.pxToSp(fontSizePx, context.resources.displayMetrics)
        Text(
            modifier = modifier,
            text = text,
            style = TextStyle(
                color = color,
                fontWeight = if (fontRes == FontRefs.DEFAULT_FONT_REF) FontWeight.Normal else FontWeight.Bold,
                fontSize = fontSizeSp.sp,
            )
        )
    } else {
        Image(
            modifier = modifier,
            provider = ImageProvider(
                text.asBitmap(
                    width = TypedValueCompat.dpToPx(
                        availableWidth.value,
                        context.resources.displayMetrics
                    ).toInt(),
                    fontSize = TypedValueCompat.dpToPx(
                        fontSize.value,
                        context.resources.displayMetrics
                    ),
                    color = Color.WHITE,
                    font = ResourcesCompat.getFont(context, fontRes),
                    lineSpacingMultiplier = lineSpacingMultiplier,
                )
            ),
            contentDescription = text,
            contentScale = ContentScale.Fit,
            colorFilter = ColorFilter.tint(color),
        )
    }
}
