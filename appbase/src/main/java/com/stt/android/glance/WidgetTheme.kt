package com.stt.android.glance

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.glance.GlanceTheme
import androidx.glance.color.ColorProviders
import androidx.glance.color.colorProviders
import androidx.glance.unit.ColorProvider

@Composable
fun HomeWidgetTheme(content: @Composable () -> Unit) {
    GlanceTheme(
        colors = widgetColorProviders(
            surface = androidx.glance.color.ColorProvider(
                day = Color.White,
                night = Color(0xFF1C1C1E),
            ),
            surfaceVariant = androidx.glance.color.ColorProvider(
                day = Color(0xFFE9ECEE),
                night = Color(0xFF3D3D3D),
            ),
        ),
        content = content,
    )
}

/**
 * https://issuetracker.google.com/issues/374967727
 * colors provided by design:
 * day:
 * onSurface = Color(0xFF303030)
 * outline = Color(0xFF7E8084)
 * night:
 * onSurface = Color.White
 * outline = Color(0xFF7E8084)
 * since it doesn't work for dayNight theme switch, we use the color resources from the system instead.
 * There is [androidx.glance.unit.ResourceColorProvider] available, but it has this comment:
 * Returns a [ColorProvider] that resolves to the color resource. This should
 * not be used outside of the Glance Libraries due to inconsistencies with regards
 * to what process (app vs launcher) colors are resolved in
 */
@Composable
private fun widgetColorProviders(
    surface: ColorProvider,
    surfaceVariant: ColorProvider,
): ColorProviders {
    val colors = GlanceTheme.colors
    return colorProviders(
        primary = colors.primary,
        onPrimary = colors.onPrimary,
        primaryContainer = colors.primaryContainer,
        onPrimaryContainer = colors.onPrimaryContainer,
        secondary = colors.secondary,
        onSecondary = colors.onSecondary,
        secondaryContainer = colors.secondaryContainer,
        onSecondaryContainer = colors.onSecondaryContainer,
        tertiary = colors.tertiary,
        onTertiary = colors.onTertiary,
        tertiaryContainer = colors.tertiaryContainer,
        onTertiaryContainer = colors.onTertiaryContainer,
        error = colors.error,
        errorContainer = colors.errorContainer,
        onError = colors.onError,
        onErrorContainer = colors.onErrorContainer,
        background = colors.background,
        onBackground = colors.onBackground,
        surface = surface,
        onSurface = colors.onSurface,
        surfaceVariant = surfaceVariant,
        onSurfaceVariant = colors.onSurfaceVariant,
        outline = colors.outline,
        inverseOnSurface = colors.inverseOnSurface,
        inverseSurface = colors.inverseSurface,
        inversePrimary = colors.inversePrimary,
    )
}
