package com.stt.android.glance

import android.appwidget.AppWidgetManager
import android.content.Context
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import com.stt.android.glance.analytics.HomeWidgetAnalytics
import javax.inject.Inject

abstract class HomeWidgetReceiver : GlanceAppWidgetReceiver() {

    @Inject
    lateinit var homeWidgetAnalytics: HomeWidgetAnalytics

    abstract val glanceAppWidgetName: String

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        super.onUpdate(context, appWidgetManager, appWidgetIds)
        homeWidgetAnalytics.onWidgetsUpdated(context, glanceAppWidgetName, appWidgetIds.toList())
    }

    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        super.onDeleted(context, appWidgetIds)
        homeWidgetAnalytics.onWidgetsDeleted(glanceAppWidgetName, appWidgetIds.toList())
    }
}
