package com.stt.android.glance

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodySmall
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleParameters
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleType
import com.stt.android.menstrualcycle.domain.MenstrualDateType

@Preview(showBackground = true)
@Composable
private fun DiaryBubbleCanvasPreview() {
    val bubbleSize = 40.dp
    val bubbleRadius = 16f
    val strokeWidth = 4f

    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text("Diary bubble types preview", style = MaterialTheme.typography.bodyLarge)

        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // TrainingDayBubbleType with multiple activities
            item {
                BubblePreviewItem(
                    label = "Training day",
                    bubbleType = DiaryBubbleType.TrainingDayBubbleType(
                        activityGroupsBubbleParameters = listOf(
                            DiaryBubbleParameters(radius = 0.8f, colorRes = android.R.color.holo_blue_bright),
                            DiaryBubbleParameters(radius = 0.6f, colorRes = android.R.color.holo_green_light),
                            DiaryBubbleParameters(radius = 0.4f, colorRes = android.R.color.holo_orange_light)
                        )
                    ),
                    menstrualDateType = MenstrualDateType.NOTHING,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Today",
                    bubbleType = DiaryBubbleType.TodayBubbleType,
                    menstrualDateType = MenstrualDateType.NOTHING,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Rest day",
                    bubbleType = DiaryBubbleType.RestDayBubbleType,
                    menstrualDateType = MenstrualDateType.NOTHING,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Planned workout",
                    bubbleType = DiaryBubbleType.FutureDateBubbleType(
                        activityGroupsBubbleParameters = listOf(
                            DiaryBubbleParameters(radius = 0.7f, colorRes = android.R.color.holo_blue_bright),
                            DiaryBubbleParameters(radius = 0.5f, colorRes = android.R.color.holo_green_light)
                        )
                    ),
                    menstrualDateType = MenstrualDateType.NOTHING,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Future date",
                    bubbleType = DiaryBubbleType.FutureDateBubbleType(
                        activityGroupsBubbleParameters = emptyList()
                    ),
                    menstrualDateType = MenstrualDateType.NOTHING,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Today",
                    bubbleType = DiaryBubbleType.TodayBubbleType,
                    menstrualDateType = MenstrualDateType.START_OF_PREDICTION,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Planned workout",
                    bubbleType = DiaryBubbleType.FutureDateBubbleType(
                        activityGroupsBubbleParameters = listOf(
                            DiaryBubbleParameters(radius = 0.7f, colorRes = android.R.color.holo_blue_bright),
                            DiaryBubbleParameters(radius = 0.5f, colorRes = android.R.color.holo_green_light)
                        )
                    ),
                    menstrualDateType = MenstrualDateType.IN_PREDICTION,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "No bubble",
                    bubbleType = DiaryBubbleType.NoBubbleType,
                    menstrualDateType = MenstrualDateType.END_OF_PREDICTION,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Start history",
                    bubbleType = DiaryBubbleType.RestDayBubbleType,
                    menstrualDateType = MenstrualDateType.START_OF_HISTORY,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "Training day",
                    bubbleType = DiaryBubbleType.TrainingDayBubbleType(
                        activityGroupsBubbleParameters = listOf(
                            DiaryBubbleParameters(radius = 0.8f, colorRes = android.R.color.holo_blue_bright),
                            DiaryBubbleParameters(radius = 0.6f, colorRes = android.R.color.holo_green_light),
                            DiaryBubbleParameters(radius = 0.4f, colorRes = android.R.color.holo_orange_light)
                        )
                    ),
                    menstrualDateType = MenstrualDateType.IN_HISTORY_NORMAL,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }

            item {
                BubblePreviewItem(
                    label = "End history",
                    bubbleType = DiaryBubbleType.TodayBubbleType,
                    menstrualDateType = MenstrualDateType.END_OF_HISTORY_NORMAL,
                    bubbleSize = bubbleSize,
                    bubbleRadius = bubbleRadius,
                    strokeWidth = strokeWidth
                )
            }
        }
    }
}

@Composable
private fun BubblePreviewItem(
    label: String,
    bubbleType: DiaryBubbleType,
    menstrualDateType: MenstrualDateType,
    bubbleSize: Dp,
    bubbleRadius: Float,
    strokeWidth: Float
) {
    val density = LocalDensity.current
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        val context = LocalContext.current
        val bitmap = generateDiaryBubble(
            context = context,
            bubbleType = bubbleType,
            menstrualDateType = menstrualDateType,
            width = with(density) { bubbleSize.toPx().toInt() },
            height = with(density) { bubbleSize.toPx().toInt() },
            bubbleRadius = bubbleRadius,
            strokeWidth = strokeWidth
        )

        Image(
            bitmap = bitmap.asImageBitmap(),
            contentDescription = label,
            modifier = Modifier.size(bubbleSize)
        )

        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
    }
}
