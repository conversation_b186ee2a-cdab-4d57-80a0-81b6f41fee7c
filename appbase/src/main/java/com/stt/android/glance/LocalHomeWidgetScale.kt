package com.stt.android.glance

import androidx.compose.runtime.Composable
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

val LocalHomeWidgetScale = compositionLocalOf { 1f }

val Int.scaledDp: Dp
    @Composable
    get() = this.dp * LocalHomeWidgetScale.current

val Float.scaledDp: Dp
    @Composable
    get() = this.dp * LocalHomeWidgetScale.current
