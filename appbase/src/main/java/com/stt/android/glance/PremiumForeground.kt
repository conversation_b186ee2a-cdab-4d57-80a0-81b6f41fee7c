package com.stt.android.glance

import android.graphics.Bitmap
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.text.TextPaint
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.applyCanvas
import androidx.core.graphics.createBitmap
import androidx.core.graphics.withRotation
import androidx.core.util.TypedValueCompat
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.layout.ContentScale
import com.stt.android.FontRefs
import com.stt.android.R
import kotlin.math.ceil
import kotlin.math.sqrt

private fun drawPremiumForeground(
    width: Int,
    height: Int,
    ribbonWidth: Int,
    text: String,
    font: Typeface?,
    fontSize: Float,
    fontColor: Int,
    ribbonColor: Int,
    backgroundColor: Int,
): Bitmap {
    val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = backgroundColor
    }
    val textPaint = TextPaint(TextPaint.ANTI_ALIAS_FLAG).apply {
        this.color = fontColor
        this.textSize = fontSize
        this.typeface = font
        this.textAlign = Paint.Align.CENTER
    }
    val textBounds = Rect()
    textPaint.getTextBounds(text, 0, text.length, textBounds)
    return createBitmap(width, height).applyCanvas {
        drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)
        val ribbonHeight = ceil(sqrt((width * width + height * height).toFloat()))
        withRotation(-45f) {
            paint.color = ribbonColor
            drawRect(
                -ribbonHeight / 2f,
                ribbonHeight / 2f - ribbonWidth / 2f,
                ribbonHeight / 2f,
                ribbonHeight / 2f + ribbonWidth / 2f,
                paint,
            )
            drawText(text, 0f, ribbonHeight / 2f + textBounds.height() / 2f, textPaint)
        }
    }
}

@Composable
fun PremiumForeground(
    width: Dp,
    height: Dp,
    modifier: GlanceModifier = GlanceModifier,
) {
    val context = LocalContext.current
    Image(
        modifier = modifier,
        provider = ImageProvider(
            drawPremiumForeground(
                width = TypedValueCompat.dpToPx(
                    width.value,
                    context.resources.displayMetrics,
                ).toInt(),
                height = TypedValueCompat.dpToPx(
                    height.value,
                    context.resources.displayMetrics,
                ).toInt(),
                ribbonWidth = TypedValueCompat.dpToPx(
                    48.scaledDp.value,
                    context.resources.displayMetrics,
                ).toInt(),
                text = context.getString(R.string.premium),
                font = ResourcesCompat.getFont(context, FontRefs.DEFAULT_FONT_REF),
                fontSize = TypedValueCompat.dpToPx(
                    18.scaledDp.value,
                    context.resources.displayMetrics,
                ),
                fontColor = Color.White.toArgb(),
                ribbonColor = Color(0xFF27A07A).toArgb(),
                backgroundColor = GlanceTheme.colors.surface
                    .getColor(context)
                    .copy(alpha = 0.5f)
                    .toArgb(),
            ),
        ),
        contentDescription = null,
        contentScale = ContentScale.Fit,
    )
}
