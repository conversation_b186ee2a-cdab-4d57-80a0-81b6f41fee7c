package com.stt.android.glance.analytics

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import androidx.core.content.edit
import com.stt.android.analytics.AnalyticsEvent.GLANCE_WIDGET_ADDED
import com.stt.android.analytics.AnalyticsEvent.GLANCE_WIDGET_REMOVED
import com.stt.android.analytics.AnalyticsEventProperty.GLANCE_WIDGET_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.GLANCE_WIDGET_NAME
import com.stt.android.di.SystemWidgetPreferences
import com.stt.android.eventtracking.EventTracker
import com.stt.android.glance.HomeWidgetReceiver
import com.stt.android.utils.STTConstants.SystemWidgetPreferences.KEY_WIDGET_ID_CACHE_PREFIX
import javax.inject.Inject

/**
 * @see com.stt.android.systemwidget.SystemWidgetAnalytics
 */
class HomeWidgetAnalytics @Inject constructor(
    @SystemWidgetPreferences private val sharedPreferences: SharedPreferences,
    private val eventTracker: EventTracker,
) {
    fun onWidgetsUpdated(context: Context, typeName: String, appWidgetIds: List<Int>) {
        val cachedIds = fetchCachedIdsForWidgetType(typeName)
        val newIds = appWidgetIds.filterNot { cachedIds.contains(it) }

        if (newIds.isNotEmpty()) {
            val totalWidgetCount = runCatching { getGlanceWidgetCount(context) }.getOrNull() ?: 0
            newIds.forEach { _ ->
                eventTracker.trackEvent(
                    GLANCE_WIDGET_ADDED,
                    mapOf(
                        GLANCE_WIDGET_NAME to typeName,
                        GLANCE_WIDGET_COUNT to totalWidgetCount,
                    ),
                )
            }

            writeCachedIdsForWidgetType(typeName, cachedIds + newIds)
        }
    }

    fun onWidgetsDeleted(typeName: String, appWidgetIds: List<Int>) {
        appWidgetIds.forEach { _ ->
            eventTracker.trackEvent(GLANCE_WIDGET_REMOVED, mapOf(GLANCE_WIDGET_NAME to typeName))
        }

        val idsToCache = fetchCachedIdsForWidgetType(typeName).toMutableList()
        idsToCache.removeAll(appWidgetIds)
        writeCachedIdsForWidgetType(typeName, idsToCache)
    }

    private fun getGlanceWidgetCount(context: Context): Int {
        val packageInfo = context.packageManager.getPackageInfo(
            context.packageName,
            PackageManager.GET_RECEIVERS,
        )
        val receivers = packageInfo?.receivers?.mapNotNull {
            runCatching {
                val cls = Class.forName(it.name)
                if (HomeWidgetReceiver::class.java.isAssignableFrom(cls)) cls else null
            }.getOrNull()
        } ?: return 0
        val manager = AppWidgetManager.getInstance(context)
        return receivers.sumOf { manager.getAppWidgetIds(ComponentName(context, it)).size }
    }

    private fun fetchCachedIdsForWidgetType(type: String): List<Int> = sharedPreferences
        .getStringSet(getPrefsKeyForCachedIds(type), emptySet())
        ?.map { it.toInt() }
        ?: emptyList()

    private fun writeCachedIdsForWidgetType(type: String, ids: List<Int>) = sharedPreferences.edit {
        putStringSet(getPrefsKeyForCachedIds(type), ids.map { it.toString() }.toSet())
    }

    private fun getPrefsKeyForCachedIds(type: String) = "$KEY_WIDGET_ID_CACHE_PREFIX${type}_GLANCE"
}
