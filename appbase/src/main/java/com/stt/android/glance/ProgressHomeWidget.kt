package com.stt.android.glance

import android.content.Context
import android.content.res.Resources
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.provideContent
import androidx.glance.color.ColorProvider
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.size
import com.stt.android.FontRefs
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.glance.action.actionStartActivityWithAnalytics
import com.stt.android.glance.data.ProgressHomeWidgetInfo
import com.stt.android.glance.dataloader.ProgressHomeWidgetDataLoader
import com.stt.android.home.HomeActivityNavigator
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import kotlin.math.min

class ProgressHomeWidget : GlanceAppWidget() {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface WidgetEntryPoint {
        fun progressHomeWidgetDataLoader(): ProgressHomeWidgetDataLoader
        fun dispatchers(): CoroutinesDispatchers
        fun homeActivityNavigator(): HomeActivityNavigator
        fun subscribedToPremiumUseCase(): IsSubscribedToPremiumUseCase
    }

    override val sizeMode = SizeMode.Exact

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        val entryPoint = EntryPointAccessors.fromApplication(
            context.applicationContext,
            WidgetEntryPoint::class.java,
        )
        val info = withContext(entryPoint.dispatchers().io) {
            entryPoint.progressHomeWidgetDataLoader().load()
        }
        val intent = entryPoint.homeActivityNavigator().newStartIntentToDiaryProgressTab(context)
        val maxSize = with(Resources.getSystem().displayMetrics) {
            min(widthPixels, heightPixels) / density
        } / 2f
        val isPremium = entryPoint.subscribedToPremiumUseCase().invoke().first()
        provideContent {
            HomeWidgetTheme {
                HomeWidgetSurface(
                    targetWidth = 158f,
                    targetHeight = 158f,
                    maxWidth = maxSize,
                    maxHeight = maxSize,
                    paddingHorizontal = 0f,
                    paddingVertical = 0f,
                    onClick = actionStartActivityWithAnalytics(intent, NAME),
                ) { availableWidth, availableHeight ->
                    Box(
                        modifier = GlanceModifier
                            .fillMaxSize()
                            .padding(16.scaledDp),
                    ) {
                        Content(
                            info = info,
                            availableWidth = availableWidth - 16.scaledDp,
                        )
                    }
                    if (!isPremium) {
                        PremiumForeground(
                            modifier = GlanceModifier.fillMaxSize(),
                            width = availableWidth,
                            height = availableHeight,
                        )
                    }
                }
            }
        }
    }

    @Composable
    fun Content(
        info: ProgressHomeWidgetInfo,
        availableWidth: Dp,
        modifier: GlanceModifier = GlanceModifier,
    ) {
        val context = LocalContext.current
        Column(modifier = modifier.fillMaxSize()) {
            Row(modifier = GlanceModifier.fillMaxWidth()) {
                Column {
                    WidgetText(
                        text = context.getString(R.string.dashboard_widget_progress_name),
                        fontRes = FontRefs.DEFAULT_FONT_BOLD_REF,
                        fontSize = 14.scaledDp,
                        color = GlanceTheme.colors.onSurface,
                    )
                    WidgetText(
                        text = context.getString(R.string.today),
                        fontRes = FontRefs.DEFAULT_FONT_REF,
                        fontSize = 12.scaledDp,
                        color = GlanceTheme.colors.outline,
                    )
                }
                Spacer(modifier = GlanceModifier.defaultWeight())
                Image(
                    modifier = GlanceModifier.size(24.scaledDp),
                    provider = ImageProvider(resId = R.drawable.dashboard_widget_progress),
                    contentDescription = null,
                )
            }
            Box(
                modifier = GlanceModifier
                    .fillMaxWidth()
                    .defaultWeight(),
                contentAlignment = Alignment.CenterStart,
            ) {
                WidgetText(
                    text = context.getString(info.contentRes),
                    fontRes = if (info.contentColor != null) FontRefs.DEFAULT_FONT_BOLD_REF else FontRefs.DEFAULT_FONT_REF,
                    fontSize = info.contentFontSize.scaledDp,
                    color = info.contentColor ?: GlanceTheme.colors.onSurface,
                    availableWidth = availableWidth,
                )
            }
            Row(modifier = GlanceModifier.fillMaxWidth()) {
                NameValueColumn(
                    modifier = GlanceModifier.defaultWeight(),
                    nameRes = R.string.home_dashboard_widgets_progress_ctl_abbrev,
                    value = info.ctl,
                    horizontalAlignment = Alignment.Start,
                )
                NameValueColumn(
                    modifier = GlanceModifier.defaultWeight(),
                    nameRes = R.string.home_dashboard_widgets_progress_atl_abbrev,
                    value = info.atl,
                )
                NameValueColumn(
                    modifier = GlanceModifier.defaultWeight(),
                    nameRes = R.string.home_dashboard_widgets_progress_tsb_abbrev,
                    value = info.tsb,
                    horizontalAlignment = Alignment.End,
                )
            }
        }
    }

    @Composable
    fun NameValueColumn(
        @StringRes nameRes: Int,
        value: Int?,
        modifier: GlanceModifier = GlanceModifier,
        horizontalAlignment: Alignment.Horizontal = Alignment.CenterHorizontally,
    ) {
        val context = LocalContext.current
        Column(
            modifier = modifier,
            horizontalAlignment = horizontalAlignment,
        ) {
            WidgetText(
                text = context.getString(nameRes),
                fontRes = FontRefs.DEFAULT_FONT_REF,
                fontSize = 12.scaledDp,
                color = ColorProvider(
                    day = Color(0xFFACAFB6),
                    night = Color(0xFF7E8084),
                ),
            )
            Spacer(modifier = GlanceModifier.height(4.scaledDp))
            WidgetText(
                text = value?.toString() ?: "-",
                fontRes = FontRefs.DEFAULT_FONT_BOLD_REF,
                fontSize = 18.scaledDp,
                color = GlanceTheme.colors.onSurface,
            )
        }
    }

    companion object {
        const val NAME = "Progress"
    }
}
