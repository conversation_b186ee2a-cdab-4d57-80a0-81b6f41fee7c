package com.stt.android.glance

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.Dp
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.applyCanvas
import androidx.core.graphics.createBitmap
import androidx.core.util.TypedValueCompat
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.LocalContext
import androidx.glance.color.ColorProvider
import androidx.glance.layout.ContentScale
import androidx.glance.layout.height
import androidx.glance.layout.width
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleParameters
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleType
import com.stt.android.menstrualcycle.domain.MenstrualDateType
import com.stt.android.menstrualcycle.domain.isNotFeatureHistory
import kotlin.math.min

@Composable
fun DiaryBubbleRow(
    bubbleList: List<DiaryBubbleContainer>,
    cellWidth: Dp,
    cellHeight: Dp,
    modifier: GlanceModifier = GlanceModifier,
) {
    val context = LocalContext.current
    val cellWidthPx = TypedValueCompat.dpToPx(
        cellWidth.value,
        context.resources.displayMetrics,
    ).toInt()
    val cellHeightPx = TypedValueCompat.dpToPx(
        cellHeight.value,
        context.resources.displayMetrics,
    ).toInt()
    val bubbleRadiusPx = TypedValueCompat.dpToPx(
        2.scaledDp.value,
        context.resources.displayMetrics,
    )
    val strokeWidthPx = TypedValueCompat.dpToPx(
        2.scaledDp.value,
        context.resources.displayMetrics,
    )
    val bitmap = createBitmap(
        width = cellWidthPx * bubbleList.size,
        height = cellHeightPx,
    ).applyCanvas {
        bubbleList.forEachIndexed { index, bubble ->
            val cellBitmap = generateDiaryBubble(
                context,
                bubble.bubbleType,
                bubble.dayData.menstrualDateType,
                width = cellWidthPx,
                height = cellHeightPx,
                bubbleRadius = bubbleRadiusPx,
                strokeWidth = strokeWidthPx,
            )
            drawBitmap(cellBitmap, (index * cellWidthPx).toFloat(), 0f, null)
        }
    }
    Image(
        modifier = modifier
            .width(cellWidth * bubbleList.size)
            .height(cellHeight),
        provider = ImageProvider(bitmap),
        contentDescription = null,
        contentScale = ContentScale.Fit,
    )
}

@Composable
internal fun generateDiaryBubble(
    context: Context,
    bubbleType: DiaryBubbleType,
    menstrualDateType: MenstrualDateType,
    width: Int,
    height: Int,
    bubbleRadius: Float,
    strokeWidth: Float,
): Bitmap {
    val restBubbleColor = if (menstrualDateType.isNotFeatureHistory()) {
        ColorProvider(day = Color.White, night = Color(0xFF7E8084))
    } else {
        ColorProvider(day = Color(0xFFD8DBDD), night = Color(0xFF7E8084))
    }

    return createBitmap(width, height).applyCanvas {
        drawMenstrualDateType(
            dateType = menstrualDateType,
            borderRadius = (height - strokeWidth) / 2f,
            strokeWidth = strokeWidth,
            color = GlanceTheme.colors.surfaceVariant.getColor(context).toArgb(),
            left = 0F,
            top = 0f,
            right = width.toFloat(),
            bottom = height.toFloat(),
        )

        when (bubbleType) {
            is DiaryBubbleType.TrainingDayBubbleType -> {
                drawTrainingDayBubble(
                    context,
                    parameters = bubbleType.activityGroupsBubbleParameters,
                    fullBubbleSize = min(height, width) / 2.0f,
                    restDayBubbleRadius = bubbleRadius,
                    width = width,
                    height = height,
                )
            }

            is DiaryBubbleType.RestDayBubbleType -> {
                drawBubble(
                    color = restBubbleColor.getColor(context).toArgb(),
                    radius = bubbleRadius,
                    width = width,
                    height = height,
                )
            }

            is DiaryBubbleType.TodayBubbleType -> {
                drawBubble(
                    color = GlanceTheme.colors.onSurface.getColor(context).toArgb(),
                    radius = bubbleRadius,
                    width = width,
                    height = height,
                )
            }

            is DiaryBubbleType.FutureDateBubbleType -> {
                if (bubbleType.activityGroupsBubbleParameters.isNotEmpty()) {
                    drawPlannedWorkoutDayBubble(
                        context,
                        parameters = bubbleType.activityGroupsBubbleParameters,
                        fullBubbleSize = min(height, width) / 2.0f,
                        restDayBubbleRadius = bubbleRadius,
                        width = width,
                        height = height,
                    )
                } else {
                    drawBubble(
                        color = GlanceTheme.colors.surfaceVariant.getColor(context).toArgb(),
                        radius = bubbleRadius,
                        width = width,
                        height = height,
                    )
                }
            }

            is DiaryBubbleType.NoBubbleType -> {
                // Do nothing
            }
        }
    }
}

private fun Canvas.drawTrainingDayBubble(
    context: Context,
    parameters: List<DiaryBubbleParameters>,
    fullBubbleSize: Float,
    restDayBubbleRadius: Float,
    width: Int,
    height: Int,
) {
    val largestBubbleForDay = parameters.maxByOrNull { it.radius }

    if (largestBubbleForDay != null && largestBubbleForDay.radius > 0.0f) {
        val bubbleScale = (restDayBubbleRadius / (largestBubbleForDay.radius * fullBubbleSize))
            .coerceAtLeast(1.0f)

        parameters.forEach { param ->
            val radiusInPixels = fullBubbleSize * param.radius * bubbleScale
            val bubbleColor =
                ResourcesCompat.getColor(context.resources, param.colorRes, context.theme)
            drawBubble(
                color = bubbleColor,
                radius = radiusInPixels,
                width = width,
                height = height,
            )
        }
    }
}

private fun Canvas.drawPlannedWorkoutDayBubble(
    context: Context,
    parameters: List<DiaryBubbleParameters>,
    fullBubbleSize: Float,
    restDayBubbleRadius: Float,
    width: Int,
    height: Int,
) {
    val largestBubbleForDay = parameters.maxByOrNull { it.radius }

    if (largestBubbleForDay != null && largestBubbleForDay.radius > 0.0f) {
        val bubbleScale = (restDayBubbleRadius / (largestBubbleForDay.radius * fullBubbleSize))
            .coerceAtLeast(1.0f)

        parameters.forEach { param ->
            val radiusInPixels = fullBubbleSize * param.radius * bubbleScale
            val bubbleColor =
                ResourcesCompat.getColor(context.resources, param.colorRes, context.theme)
            drawOutlinedBubble(
                color = bubbleColor,
                radius = radiusInPixels,
                width = width,
                height = height,
                strokeWidth = TypedValueCompat.dpToPx(2f, context.resources.displayMetrics),
            )
        }
    }
}

private fun Canvas.drawBubble(color: Int, radius: Float, width: Int, height: Int) {
    drawCircle(
        width / 2f,
        height / 2f,
        radius,
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            this.color = color
            this.style = Paint.Style.FILL
        },
    )
}

private fun Canvas.drawOutlinedBubble(
    color: Int,
    radius: Float,
    width: Int,
    height: Int,
    strokeWidth: Float
) {
    drawCircle(
        width / 2f,
        height / 2f,
        radius,
        Paint(Paint.ANTI_ALIAS_FLAG).apply {
            this.color = color
            this.style = Paint.Style.STROKE
            this.strokeWidth = strokeWidth
        },
    )
}

fun Canvas.drawMenstrualDateType(
    dateType: MenstrualDateType,
    borderRadius: Float = 0f,
    strokeWidth: Float = 4f,
    color: Int,
    left: Float,
    top: Float,
    right: Float,
    bottom: Float
) {
    val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        this.color = color
        when (dateType) {
            MenstrualDateType.IN_HISTORY_AFTER_TODAY,
            MenstrualDateType.IN_PREDICTION,
            MenstrualDateType.START_OF_PREDICTION,
            MenstrualDateType.END_OF_PREDICTION,
            MenstrualDateType.END_OF_HISTORY_AFTER_TODAY -> {
                this.style = Paint.Style.STROKE
                this.strokeWidth = strokeWidth
                this.strokeCap = Paint.Cap.ROUND
            }

            else -> {
                this.style = Paint.Style.FILL
            }
        }
    }

    val lineWidth = when (dateType) {
        MenstrualDateType.IN_HISTORY_AFTER_TODAY,
        MenstrualDateType.IN_PREDICTION,
        MenstrualDateType.START_OF_PREDICTION,
        MenstrualDateType.END_OF_PREDICTION,
        MenstrualDateType.END_OF_HISTORY_AFTER_TODAY -> strokeWidth

        else -> 0f
    }
    when (dateType) {
        MenstrualDateType.IN_HISTORY_AFTER_TODAY,
        MenstrualDateType.IN_PREDICTION -> {
            drawLine(
                left,
                top + lineWidth / 2,
                right,
                top + lineWidth / 2,
                paint,
            )
            drawLine(
                left,
                bottom - lineWidth / 2,
                right,
                bottom - lineWidth / 2,
                paint,
            )
        }

        MenstrualDateType.IN_HISTORY_NORMAL -> {
            drawRect(left, top, right, bottom, paint)
        }

        MenstrualDateType.START_OF_PREDICTION,
        MenstrualDateType.START_OF_HISTORY -> {
            drawStartMenstrualDateType(
                left = left + lineWidth / 2,
                top = top + lineWidth / 2,
                right = right,
                bottom = bottom - lineWidth / 2,
                borderRadius = borderRadius,
                paint = paint,
            )
        }

        MenstrualDateType.END_OF_PREDICTION,
        MenstrualDateType.END_OF_HISTORY_AFTER_TODAY,
        MenstrualDateType.END_OF_HISTORY_NORMAL -> {
            drawEndMenstrualDateType(
                left = left,
                top = top + lineWidth / 2,
                right = right - lineWidth / 2,
                bottom = bottom - lineWidth / 2,
                borderRadius = borderRadius,
                paint = paint,
            )
        }

        MenstrualDateType.BOTH_START_AND_END_IN_HISTORY,
        MenstrualDateType.BOTH_START_AND_END_IN_PREDICTION -> {
            drawStartAndEndMenstrualDateType(
                left = left,
                top = top + lineWidth / 2,
                right = right - lineWidth / 2,
                bottom = bottom - lineWidth / 2,
                borderRadius = borderRadius,
                paint = paint,
            )
        }

        MenstrualDateType.NOTHING -> Unit
    }
}

private fun Canvas.drawStartMenstrualDateType(
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
    borderRadius: Float,
    paint: Paint,
) {
    val path = Path().apply {
        moveTo(right, bottom)
        lineTo(left + borderRadius, bottom)
        arcTo(
            RectF(left, bottom - borderRadius * 2, left + borderRadius * 2, bottom),
            90f,
            90f,
            false
        )
        lineTo(left, top + borderRadius)
        arcTo(
            RectF(left, top, left + borderRadius * 2, top + borderRadius * 2),
            180f,
            90f,
            false
        )
        lineTo(right, top)
    }
    drawPath(path, paint)
}

private fun Canvas.drawEndMenstrualDateType(
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
    borderRadius: Float,
    paint: Paint,
) {
    val path = Path().apply {
        moveTo(left, top)
        lineTo(right - borderRadius, top)
        arcTo(
            RectF(right - borderRadius * 2, top, right, top + borderRadius * 2),
            270f,
            90f,
            false
        )
        lineTo(right, bottom - borderRadius)
        arcTo(
            RectF(right - borderRadius * 2, bottom - borderRadius * 2, right, bottom),
            0f,
            90f,
            false
        )
        lineTo(left, bottom)
    }
    drawPath(path, paint)
}

private fun Canvas.drawStartAndEndMenstrualDateType(
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
    borderRadius: Float,
    paint: Paint,
) {
    val path = Path().apply {
        moveTo(left, top)
        lineTo(right - borderRadius, top)
        arcTo(
            RectF(right - borderRadius * 2, top, right, top + borderRadius * 2),
            270f,
            90f,
            false
        )
        lineTo(right, bottom - borderRadius)
        arcTo(
            RectF(right - borderRadius * 2, bottom - borderRadius * 2, right, bottom),
            0f,
            90f,
            false
        )
        lineTo(left, bottom)
    }
    drawPath(path, paint)
}
