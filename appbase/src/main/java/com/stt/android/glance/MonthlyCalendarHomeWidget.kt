package com.stt.android.glance

import android.content.Context
import android.content.res.Resources
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.Dp
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.GlanceTheme
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.SizeMode
import androidx.glance.appwidget.provideContent
import androidx.glance.layout.Alignment
import androidx.glance.layout.Box
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import com.stt.android.FontRefs
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.glance.action.actionStartActivityWithAnalytics
import com.stt.android.glance.data.MonthlyCalendarHomeWidgetInfo
import com.stt.android.glance.dataloader.MonthlyCalendarHomeWidgetDataLoader
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.home.dashboardv2.ui.widgets.common.monthLabel
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleContainer
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.CalendarUtils
import com.stt.android.utils.iterator
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.withContext
import java.time.LocalDate
import kotlin.math.min

class MonthlyCalendarHomeWidget : GlanceAppWidget() {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface WidgetEntryPoint {
        fun monthlyCalendarHomeWidgetDataLoader(): MonthlyCalendarHomeWidgetDataLoader
        fun dispatchers(): CoroutinesDispatchers
        fun homeActivityNavigator(): HomeActivityNavigator
    }

    override val sizeMode = SizeMode.Exact

    override suspend fun provideGlance(context: Context, id: GlanceId) {
        val entryPoint = EntryPointAccessors.fromApplication(
            context.applicationContext,
            WidgetEntryPoint::class.java,
        )
        val info = withContext(entryPoint.dispatchers().io) {
            entryPoint.monthlyCalendarHomeWidgetDataLoader().load()
        }
        val intent = entryPoint.homeActivityNavigator().newStartIntentToDiaryCalendar(
            context,
            "HomeWidgetCalendar",
        )
        val maxSize = with(Resources.getSystem().displayMetrics) {
            min(widthPixels, heightPixels) / density
        } / 2f
        provideContent {
            HomeWidgetTheme {
                HomeWidgetSurface(
                    targetWidth = 158f,
                    targetHeight = 158f,
                    maxWidth = maxSize,
                    maxHeight = maxSize,
                    paddingHorizontal = 16f,
                    paddingVertical = 16f,
                    onClick = actionStartActivityWithAnalytics(intent, NAME),
                ) { availableWidth, availableHeight ->
                    Content(
                        info = info,
                        availableWidth = availableWidth,
                        availableHeight = availableHeight,
                    )
                }
            }
        }
    }

    @Composable
    fun Content(
        info: MonthlyCalendarHomeWidgetInfo,
        availableWidth: Dp,
        availableHeight: Dp,
        modifier: GlanceModifier = GlanceModifier,
    ) {
        Column(modifier = modifier.fillMaxSize()) {
            WidgetText(
                modifier = GlanceModifier.padding(bottom = 8.scaledDp),
                text = LocalDate.now().monthLabel,
                fontRes = FontRefs.DEFAULT_FONT_BOLD_REF,
                fontSize = 14.scaledDp,
                color = GlanceTheme.colors.onSurface,
            )
            CalendarWeekLabels(
                calendarProvider = info.calendarProvider,
                weekDayLabels = info.bubbleData.weekDayLabels,
            )
            CalendarDiaryBubbleList(
                calendarProvider = info.calendarProvider,
                diaryBubbleData = info.bubbleData,
                availableWidth = availableWidth,
                availableHeight = availableHeight - 38.scaledDp,
            )
        }
    }

    @Composable
    private fun CalendarWeekLabels(
        calendarProvider: CalendarProvider,
        weekDayLabels: List<String>,
        modifier: GlanceModifier = GlanceModifier,
    ) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .height(16.scaledDp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            val currentIndex =
                CalendarUtils.getTodayWeekLabelIndex(calendarProvider.getDayOfWeekField())
            weekDayLabels.forEachIndexed { index, label ->
                val isCurrent = index == currentIndex
                WidgetText(
                    modifier = GlanceModifier.defaultWeight(),
                    text = label,
                    fontRes = with(FontRefs) {
                        if (isCurrent) DEFAULT_FONT_BOLD_REF else DEFAULT_FONT_REF
                    },
                    fontSize = 12.scaledDp,
                    color = if (isCurrent) GlanceTheme.colors.onSurface else GlanceTheme.colors.outline,
                )
            }
        }
    }

    @Composable
    private fun CalendarDiaryBubbleList(
        calendarProvider: CalendarProvider,
        diaryBubbleData: DiaryBubbleData,
        availableWidth: Dp,
        availableHeight: Dp,
        modifier: GlanceModifier = GlanceModifier,
    ) {
        val emptyBubbleContainer = mutableListOf<DiaryBubbleContainer>()
        val dayOfWeek = calendarProvider.getDayOfWeekField()
        val startOfWeek = diaryBubbleData.startDate.with(dayOfWeek, 1)
        val startOfMonth = diaryBubbleData.startDate
        (startOfWeek..startOfMonth.minusDays(1L)).iterator().forEach { _ ->
            emptyBubbleContainer.add(DiaryBubbleContainer.EMPTY)
        }
        val bubbleData = emptyBubbleContainer + diaryBubbleData.bubbles

        val columnCount = 7
        val rowCount = (bubbleData.size + 6) / columnCount
        val cellWidth = availableWidth / columnCount
        val cellHeight = (availableHeight - ((rowCount - 1) * 2).scaledDp) / rowCount
        repeat(rowCount) { rowIndex ->
            Box(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(bottom = if (rowIndex == rowCount - 1) 0.scaledDp else 2.scaledDp),
            ) {
                DiaryBubbleRow(
                    bubbleList = (rowIndex * columnCount..<(rowIndex + 1) * columnCount).map {
                        bubbleData.getOrNull(it) ?: DiaryBubbleContainer.EMPTY
                    },
                    cellWidth = cellWidth,
                    cellHeight = cellHeight,
                )
            }
        }
    }

    companion object {
        const val NAME = "Calendar"
    }
}
