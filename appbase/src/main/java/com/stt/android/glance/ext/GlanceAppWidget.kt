package com.stt.android.glance.ext

import android.annotation.SuppressLint
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProviderInfo
import android.content.ComponentName
import android.content.Context
import android.os.Build
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.GlanceAppWidgetReceiver
import androidx.glance.appwidget.compose
import androidx.glance.session.GlanceSessionManager
import com.stt.android.coroutines.runSuspendCatching

@SuppressLint("RestrictedApi")
suspend fun GlanceAppWidget.forceUpdateAll(context: Context) = runSuspendCatching {
    val manager = GlanceAppWidgetManager(context)
    manager.getGlanceIds(javaClass).forEach { glanceId ->
        GlanceSessionManager.runWithLock {
            // androidx.glance.appwidget.AppWidgetUtils#createUniqueRemoteUiName
            this.closeSession("appWidget-${manager.getAppWidgetId(glanceId)}")
        }
        update(context, glanceId)
    }
}

suspend fun GlanceAppWidget.updatePreview(
    context: Context,
    receiverCls: Class<out GlanceAppWidgetReceiver>,
) = runSuspendCatching {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
        AppWidgetManager.getInstance(context).setWidgetPreview(
            ComponentName(context, receiverCls),
            AppWidgetProviderInfo.WIDGET_CATEGORY_HOME_SCREEN,
            compose(context = context),
        )
    }
}
