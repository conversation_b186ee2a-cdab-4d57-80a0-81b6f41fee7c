package com.stt.android.models

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.MapPreferences
import com.stt.android.di.PremiumRequiredMyTracksGranularityTypes
import com.stt.android.domain.user.HeatmapType
import com.stt.android.domain.user.RoadSurfaceType
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.maps.HeatmapTypes
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.isAvailable
import com.stt.android.remote.map.MapTypesApi
import com.stt.android.remote.map.RemoteMap
import com.stt.android.remote.map.RemoteMapTypes
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MapSelectionModelImpl @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    private val mapTypesApi: MapTypesApi,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    @MapPreferences private val mapPreferences: SharedPreferences,
    @HeatmapTypes private val heatmapTypes: List<HeatmapType>,
    private val roadSurfaceTypes: List<RoadSurfaceType>,
    @PremiumRequiredMyTracksGranularityTypes private val premiumRequiredMyTracksGranularities: Set<MyTracksGranularity.Type>,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val dispatchers: CoroutinesDispatchers
) : MapSelectionModel {
    private val scope = CoroutineScope(dispatchers.io + SupervisorJob())
    private val isSubscribedToPremium: StateFlow<Boolean?> = isSubscribedToPremiumUseCase()
        .stateIn(scope, SharingStarted.Eagerly, null)
    private val isDynamicMapTypesSupported: Boolean = !FlavorUtils.isSuuntoAppChina

    override var selectedMapType: MapType
        get() {
            val mapType = userSettingsController.settings.selectedMapType
            val isMapTypeAvailable = mapType.isAvailable(
                currentUserController = currentUserController,
                userSettingsController = userSettingsController,
            )
            if (!isMapTypeAvailable) {
                storeSelectedMapType(MapTypeHelper.DEFAULT_MAP_TYPE)
                return MapTypeHelper.DEFAULT_MAP_TYPE
            }
            return mapType
        }
        set(value) {
            storeSelectedMapType(value)
        }

    private fun storeSelectedMapType(mapType: MapType) {
        val currentUserSettings = userSettingsController.settings
        if (currentUserSettings.selectedMapType == mapType) {
            return
        }

        userSettingsController.storeSettings(currentUserSettings.setSelectedMapType(mapType))
    }

    override val selectedMapTypeUserHasAccessTo: MapType
        get() = selectedMapType.takeUnless { it.requiresPremium && isSubscribedToPremium.value == false }
            ?: MapTypeHelper.DEFAULT_MAP_TYPE

    override var selectedHeatmap: HeatmapType?
        get() {
            val heatmapName = mapPreferences.getString(
                STTConstants.MapPreferences.KEY_SELECTED_HEATMAP,
                null
            )
            return heatmapTypes.find { it.name == heatmapName }
        }
        set(value) {
            mapPreferences.edit {
                if (value != null) {
                    putString(STTConstants.MapPreferences.KEY_SELECTED_HEATMAP, value.name)
                } else {
                    remove(STTConstants.MapPreferences.KEY_SELECTED_HEATMAP)
                }
            }
        }

    override val selectedHeatmapOrNullIfAccessDenied: HeatmapType?
        get() {
            val currentSelection = selectedHeatmap
            return if (currentSelection?.requiresPremium == true && isSubscribedToPremium.value == false) {
                null
            } else {
                currentSelection
            }
        }

    override var selectedRoadSurfaces: List<RoadSurfaceType>
        get() {
            val selectedRoadSurfaceNames = mapPreferences.getStringSet(
                STTConstants.MapPreferences.KEY_SELECTED_ROAD_SURFACES,
                null
            ) ?: emptySet()
            return roadSurfaceTypes.filter { selectedRoadSurfaceNames.contains(it.name) }
        }
        set(value) {
            val selectedRoadSurfaceNames = value.map { it.name }.toSet()
            mapPreferences.edit {
                putStringSet(
                    STTConstants.MapPreferences.KEY_SELECTED_ROAD_SURFACES,
                    selectedRoadSurfaceNames
                )
            }
        }

    override val selectedRoadSurfacesUserHasAccessTo: List<RoadSurfaceType>
        get() {
            val currentSelections = selectedRoadSurfaces
            return if (isSubscribedToPremium.value == false) {
                currentSelections.filterNot { it.requiresPremium }
            } else {
                currentSelections
            }
        }

    override var hideCyclingForbiddenRoads: Boolean
        get() = mapPreferences.getBoolean(
            STTConstants.MapPreferences.KEY_HIDE_CYCLING_FORBIDDEN_ROADS,
            false
        )
        set(value) {
            mapPreferences.edit {
                putBoolean(STTConstants.MapPreferences.KEY_HIDE_CYCLING_FORBIDDEN_ROADS, value)
            }
        }

    override var selectedMyTracksGranularity: MyTracksGranularity?
        get() {
            val type = MyTracksGranularity.Type.valueOfStringOrNull(
                mapPreferences.getString(
                    STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_GRANULARITY,
                    null
                )
            )
            val startMillis = mapPreferences.getLong(
                STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_START_DATE,
                -1L
            )
            val endMillis = mapPreferences.getLong(
                STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_END_DATE,
                -1L
            )
            return if (
                type == MyTracksGranularity.Type.CUSTOM_DATES &&
                startMillis != -1L &&
                endMillis != -1L
            ) {
                // Fit the range to allowed window and do a sanity check
                val minStartMillis = MyTracksGranularity.getMinimumAllowedRangeStartMillis()
                val rangeStartMillis = startMillis.coerceAtLeast(minStartMillis)
                if (rangeStartMillis <= endMillis) {
                    MyTracksGranularity(
                        type,
                        rangeStartMillis,
                        endMillis
                    )
                } else {
                    MyTracksGranularity(type, null, null)
                }
            } else {
                if (type != null) {
                    MyTracksGranularity(type, null, null)
                } else {
                    null
                }
            }
        }
        set(granularity) {
            mapPreferences.edit {
                if (granularity == null) {
                    remove(STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_GRANULARITY)
                    remove(STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_START_DATE)
                    remove(STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_END_DATE)
                } else if (granularity.type != MyTracksGranularity.Type.CUSTOM_DATES) {
                    putString(
                        STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_GRANULARITY,
                        granularity.type.value
                    )
                    remove(STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_START_DATE)
                    remove(STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_END_DATE)
                } else if (
                    granularity.rangeStartMillis != null && granularity.rangeEndMillis != null
                ) {
                    putString(
                        STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_GRANULARITY,
                        granularity.type.value
                    )
                    putLong(
                        STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_START_DATE,
                        granularity.rangeStartMillis
                    )
                    putLong(
                        STTConstants.MapPreferences.KEY_SELECTED_MY_TRACKS_CUSTOM_END_DATE,
                        granularity.rangeEndMillis
                    )
                }
            }
        }

    override val selectedMyTracksGranularityOrNullIfAccessDenied: MyTracksGranularity?
        get() {
            val currentSelection = selectedMyTracksGranularity
            return if (premiumRequiredMyTracksGranularities.contains(currentSelection?.type) &&
                isSubscribedToPremium.value == false
            ) {
                null
            } else {
                currentSelection
            }
        }

    override var showPOIs: Boolean
        get() = mapPreferences.getBoolean(STTConstants.MapPreferences.KEY_SHOW_POIS, true)
        set(enabled) {
            mapPreferences.edit {
                putBoolean(STTConstants.MapPreferences.KEY_SHOW_POIS, enabled)
            }
        }

    override var turnByTurnEnabled: Boolean
        get() = mapPreferences.getBoolean(
            STTConstants.MapPreferences.KEY_TURN_BY_TURN_ENABLED,
            true
        )
        set(enabled) {
            mapPreferences.edit {
                putBoolean(STTConstants.MapPreferences.KEY_TURN_BY_TURN_ENABLED, enabled)
            }
        }

    override var map3dEnabled: Boolean
        get() = mapPreferences.getBoolean(STTConstants.MapPreferences.KEY_MAP_3D_ENABLED, false)
        set(enabled) {
            mapPreferences.edit {
                putBoolean(STTConstants.MapPreferences.KEY_MAP_3D_ENABLED, enabled)
            }
        }

    override fun loadBuiltInMapTypes(): List<MapType> = MapTypeHelper.getBuiltInMaps()

    override suspend fun loadDynamicMapTypes(): List<MapType> {
        if (!isDynamicMapTypesSupported) {
            return emptyList()
        }

        fetchAndStoreDynamicMapTypes()
        return MapTypeHelper.getDynamicMaps()
    }

    override suspend fun fetchAndStoreDynamicMapTypes() = withContext(dispatchers.io) {
        if (!isDynamicMapTypesSupported) {
            return@withContext
        }

        MapTypeHelper.loadDynamicMapsFromCache(applicationContext)
        if (MapTypeHelper.isCacheValid(applicationContext)) {
            // no need to hit our server too frequently
            return@withContext
        }

        runSuspendCatching {
            val dynamicMaps = mapTypesApi.getMapTypes()
                .toMapTypes(locale = Locale(applicationContext.getString(R.string.language_code)))

            MapTypeHelper.saveDynamicMaps(applicationContext, dynamicMaps)
        }.onFailure { e ->
            Timber.w(e, "Failed to fetch dynamic map types from backend")
            MapTypeHelper.loadDynamicMapsFromCache(applicationContext)
        }
    }

    override fun loadHeatmapTypes(): List<HeatmapType> {
        return heatmapTypes
    }

    override fun loadRoadSurfaceTypes(): List<RoadSurfaceType> {
        return roadSurfaceTypes
    }

    override fun hasPremiumRequiredSelections(): Flow<Boolean> = callbackFlow {
        fun updateValue() {
            val hasPremiumRequiredSelections = selectedMapType.requiresPremium ||
                selectedHeatmap?.requiresPremium == true ||
                premiumRequiredMyTracksGranularities.contains(selectedMyTracksGranularity?.type) ||
                selectedRoadSurfaces.any { it.requiresPremium }
            trySend(hasPremiumRequiredSelections)
        }

        updateValue()

        val updateOnUserSettingsChangeListener = UserSettingsController.UpdateListener {
            updateValue()
        }
        val updateOnMapPrefsChangeListener =
            SharedPreferences.OnSharedPreferenceChangeListener { _, _ ->
                updateValue()
            }

        userSettingsController.addUpdateListener(updateOnUserSettingsChangeListener)
        mapPreferences.registerOnSharedPreferenceChangeListener(updateOnMapPrefsChangeListener)

        awaitClose {
            userSettingsController.removeUpdateListener(updateOnUserSettingsChangeListener)
            mapPreferences.unregisterOnSharedPreferenceChangeListener(updateOnMapPrefsChangeListener)
        }
    }

    override val show3dOption: Boolean
        get() = isSubscribedToPremium.value == true

    override fun clearPremiumRequiredSelections() {
        if (selectedMapType.requiresPremium) {
            selectedMapType = MapTypeHelper.DEFAULT_MAP_TYPE
        }

        if (selectedHeatmap?.requiresPremium == true) {
            selectedHeatmap = null
        }

        if (premiumRequiredMyTracksGranularities.contains(selectedMyTracksGranularity?.type)) {
            selectedMyTracksGranularity = null
        }

        selectedRoadSurfaces = selectedRoadSurfaces.filterNot { it.requiresPremium }
    }

    override var selectedPopularRoutesActivityTypes: List<ActivityType>?
        get() {
            val activityTypesString =
                mapPreferences.getString(
                    STTConstants.MapPreferences.KEY_SELECTED_POPULAR_ROUTE_ACTIVITY_TYPES,
                    ""
                )
            return if (activityTypesString.isNullOrEmpty())
                null
            else activityTypesString.split(",").map { activityType ->
                ActivityType.valueOf(activityType.toInt())
            }
        }
        set(value) {
            val activityTypesString = if (value.isNullOrEmpty()) {
                ""
            } else {
                value.map { it.id }.joinToString(",")
            }
            mapPreferences.edit {
                putString(
                    STTConstants.MapPreferences.KEY_SELECTED_POPULAR_ROUTE_ACTIVITY_TYPES,
                    activityTypesString
                )
            }
        }

    private companion object {
        fun RemoteMapTypes.toMapTypes(locale: Locale): List<MapType> =
            maps.mapNotNull { it.toMapType(locale) }

        private fun RemoteMap.toMapType(locale: Locale): MapType? {
            if (appTypes.isNotEmpty()) {
                if (appTypes.none(FlavorUtils::isCurrentFlavor)) {
                    return null
                }
            }

            val requiresFieldTester = visibleForRoles.isNotEmpty() &&
                visibleForRoles.any { role ->
                    (FlavorUtils.isSuuntoAppGlobal && role == "fieldtester") || (FlavorUtils.isSportsTracker && role == "sttester")
                }

            return MapType(
                name = name,
                requiresPremium = requiresPremium,
                requiresFieldTester = requiresFieldTester,
                titleText = localizedTitles[locale.language] ?: title,
                provider = provider,
                iconUrl = iconUrl,
                styleUrl = styleUrl,
                credit = credit,
                availableCountries = availableCountries,
            )
        }
    }
}
