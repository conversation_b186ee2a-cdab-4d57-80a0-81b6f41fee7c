package com.stt.android.models

import com.stt.android.domain.user.HeatmapType
import com.stt.android.domain.user.RoadSurfaceType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.ui.map.selection.MyTracksGranularity
import kotlinx.coroutines.flow.Flow

interface MapSelectionModel {
    fun loadBuiltInMapTypes(): List<MapType>
    suspend fun loadDynamicMapTypes(): List<MapType>
    suspend fun fetchAndStoreDynamicMapTypes()
    fun loadHeatmapTypes(): List<HeatmapType>
    fun loadRoadSurfaceTypes(): List<RoadSurfaceType>

    fun hasPremiumRequiredSelections(): Flow<Boolean>
    fun clearPremiumRequiredSelections()

    var selectedMapType: MapType

    /**
     * Either [selectedMapType] or if it requires Premium that user doesn't have, [MapTypeHelper.DEFAULT_MAP_TYPE].
     * Maps that don't have the Premium required popup and controls to change the map type should check
     * the map type to use from this field instead of [selectedMapType].
     */
    val selectedMapTypeUserHasAccessTo: MapType

    var selectedHeatmap: HeatmapType?

    /**
     * Either [selectedHeatmap] or if it requires Premium that user doesn't have, null.
     * Maps that don't have the Premium required popup and controls to change the heatmaps should check
     * the heatmap type to use from this field instead of [selectedHeatmap].
     */
    val selectedHeatmapOrNullIfAccessDenied: HeatmapType?

    var selectedRoadSurfaces: List<RoadSurfaceType>

    /**
     * [selectedRoadSurfaces] with surface types that require Premium filtered out if the user doesn't
     * have Premium access.
     * Maps that don't have the Premium required popup and controls to change the surfaces should check
     * the surfaces to use from this field instead of [selectedRoadSurfaces].
     */
    val selectedRoadSurfacesUserHasAccessTo: List<RoadSurfaceType>

    var hideCyclingForbiddenRoads: Boolean

    var selectedMyTracksGranularity: MyTracksGranularity?

    /**
     * Either [selectedMyTracksGranularity] or if it requires Premium that user doesn't have, null.
     * Maps that don't have the Premium required popup and controls to change the granularity should check
     * the granularity to use from this field instead of [selectedMyTracksGranularity].
     */
    val selectedMyTracksGranularityOrNullIfAccessDenied: MyTracksGranularity?

    var showPOIs: Boolean
    var turnByTurnEnabled: Boolean
    var map3dEnabled: Boolean
    val show3dOption: Boolean
    var selectedPopularRoutesActivityTypes: List<ActivityType>?
}
