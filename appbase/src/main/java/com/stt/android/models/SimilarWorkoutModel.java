package com.stt.android.models;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import com.stt.android.controllers.BackendController;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.WorkoutHeaderController;
import com.stt.android.domain.Point;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.utils.CoordinateUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

@Singleton
public class SimilarWorkoutModel {
    @SuppressWarnings("WeakerAccess")
    final CurrentUserController currentUserController;
    @SuppressWarnings("WeakerAccess")
    final WorkoutHeaderController workoutHeaderController;
    private final BackendController backendController;

    @Inject
    public SimilarWorkoutModel(CurrentUserController currentUserController,
        WorkoutHeaderController workoutHeaderController, BackendController backendController) {
        this.currentUserController = currentUserController;
        this.workoutHeaderController = workoutHeaderController;
        this.backendController = backendController;
    }

    @NonNull
    @WorkerThread
    public List<WorkoutHeader> findRecentWorkoutsOfSimilarDistance(
        @NonNull WorkoutHeader referenceWorkout
    ) {
        if (referenceWorkout.getActivityType().isIndoor()) {
            return Collections.emptyList();
        }

        try {
            List<WorkoutHeader> similarWorkouts =
                workoutHeaderController.findWithSimilarDistance(referenceWorkout);
            List<WorkoutHeader> recentWorkouts =
                beforeStartTime(similarWorkouts, referenceWorkout.getStartTime());
            return recentWorkouts.size() > 1 ? recentWorkouts : Collections.emptyList();
        } catch (InternalDataException e) {
            Timber.w(e, "Failed to find workouts of similar distance");
            return Collections.emptyList();
        }
    }

    /**
     * Finds workouts with same activity type, on similar route, from the same owner, and started
     * no later than the reference workout.
     */
    @NonNull
    @WorkerThread
    public List<WorkoutHeader> findRecentWorkoutsOnSimilarRoute(
        @NonNull WorkoutHeader referenceWorkout
    ) {
        if (referenceWorkout.getActivityType().isIndoor()) {
            return Collections.emptyList();
        }

        List<WorkoutHeader> similarWorkouts =
            currentUserController.getUsername().equals(referenceWorkout.getUsername())
                ? findWorkoutsOnSimilarRouteFromDb(referenceWorkout)
                : findWorkoutsOnSimilarRouteFromBackend(referenceWorkout);
        List<WorkoutHeader> recentWorkouts =
            beforeStartTime(similarWorkouts, referenceWorkout.getStartTime());
        return recentWorkouts.size() > 1 ? recentWorkouts : Collections.emptyList();
    }

    @NonNull
    @WorkerThread
    private List<WorkoutHeader> findWorkoutsOnSimilarRouteFromBackend(
        @NonNull WorkoutHeader referenceWorkout
    ) {
        try {
            return backendController.fetchWorkoutsOnSimilarRoute(
                currentUserController.getSession(),
                referenceWorkout,
                workoutHeaderController);
        } catch (Exception e) {
            Timber.w(e, "Failed to find workouts on similar route from backend");
            return findWorkoutsOnSimilarRouteFromDb(referenceWorkout);
        }
    }

    @NonNull
    @WorkerThread
    private List<WorkoutHeader> findWorkoutsOnSimilarRouteFromDb(
        @NonNull WorkoutHeader referenceWorkout
    ) {
        try {
            return workoutHeaderController.findWithSimilarRoute(referenceWorkout);
        } catch (Exception e) {
            Timber.w(e, "Failed to find workouts on similar route from DB");
            return Collections.emptyList();
        }
    }

    @NonNull
    private static List<WorkoutHeader> beforeStartTime(List<WorkoutHeader> workouts, long startTime) {
        int count = workouts.size();
        List<WorkoutHeader> filtered = new ArrayList<>(count);
        for (int i = 0; i < count; ++i) {
            WorkoutHeader workout = workouts.get(i);
            if (workout.getStartTime() <= startTime) {
                filtered.add(workout);
            }
        }
        return filtered;
    }

    /**
     * Finds workouts with same activity type, from the same owner, and started no later than the
     * reference workout.
     */
    @WorkerThread
    @NonNull
    public List<WorkoutHeader> findRecentWorkout(
        @NonNull WorkoutHeader referenceWorkout, long limit) throws InternalDataException {
        return workoutHeaderController.findLatestNotDeletedWorkouts(
            referenceWorkout.getUsername(), referenceWorkout.getActivityType(),
            referenceWorkout.getStartTime(), limit);
    }

    @WorkerThread
    @NonNull
    public List<WorkoutHeader> findWorkoutsWithSimilarStartPosition(
        @NonNull String username,
        @NonNull ActivityType activityType,
        @NonNull Point startPosition
    ) throws InternalDataException {
        return workoutHeaderController.findNotDeletedWorkouts(username, activityType)
            .stream()
            .filter(workoutHeader -> {
                Point targetStartPoint = workoutHeader.getStartPosition();
                if (targetStartPoint == null) {
                    return false;
                }
                double distance = CoordinateUtils.distanceBetween(
                    startPosition.getLatitude(),
                    startPosition.getLongitude(),
                    targetStartPoint.getLatitude(),
                    targetStartPoint.getLongitude());
                // TODO how near is "near"?
                return distance <= 1000.0D;
            })
            .collect(Collectors.toList());
    }
}
