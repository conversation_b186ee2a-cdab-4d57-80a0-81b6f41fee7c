package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import java.sql.SQLException;

public class DatabaseUpgrade6To7<PERSON><PERSON>per extends DatabaseUpgradeHelper {
    public DatabaseUpgrade6To7Helper(SQLiteDatabase db, ConnectionSource connectionSource,
                                     DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        // added average and max cadence to WorkoutHeader
        db.execSQL("ALTER TABLE " + LegacyWorkoutHeader.TABLE_NAME + " ADD COLUMN " + LegacyWorkoutHeader.DbFields.AVERAGE_CADENCE + ";");
        db.execSQL("ALTER TABLE " + LegacyWorkoutHeader.TABLE_NAME + " ADD COLUMN " + LegacyWorkoutHeader.DbFields.MAX_CADENCE + ";");
    }
}
