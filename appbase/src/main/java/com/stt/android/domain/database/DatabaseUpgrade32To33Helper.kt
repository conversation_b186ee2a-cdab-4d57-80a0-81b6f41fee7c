@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.database.deprecated.OldRouteTable
import java.sql.SQLException

class DatabaseUpgrade32To33Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) :
    DatabaseUpgradeHelper(
        db,
        connectionSource,
        databaseHelper
    ) {

    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.TOTAL_ASCENT)
    }
}
