package com.stt.android.domain.workout;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.VisibleForTesting;
import com.google.android.gms.maps.model.LatLng;
import com.google.gson.annotations.SerializedName;
import java.util.Locale;

/**
 * A geo point with some workout-specific data.
 */
public class WorkoutGeoPoint implements Parcelable {
    @SerializedName("latitudeE6")
    private final int latitudeE6;
    @SerializedName("longitudeE6")
    private final int longitudeE6;
    @SerializedName("position")
    private LatLng latLng;
    @SerializedName("altitude")
    private final double altitude;
    @SerializedName("hasAltitude")
    private final boolean hasAltitude;
    @SerializedName("speedMetersPerSecond")
    private final float speedMetersPerSecond;
    //distance from last WorkoutGeoPoint
    @SerializedName("distance")
    private final double distance;
    //Millis from start of workout minus pause time
    @SerializedName("millisecondsInWorkout")
    private final int millisecondsInWorkout;
    @SerializedName("totalDistance")
    private final double totalDistance;
    @SerializedName("course")
    private final float course;
    @SerializedName("timestamp")
    private final long timestamp;

    /**
     * Constructs a new workout geo point.
     *
     * @param latitudeE6 latitude in microdegrees (E6 format)
     * @param longitudeE6 longitude in microdegrees (E6 format)
     * @param altitude altitude in meters
     * @param hasAltitude if this point has a valid altitude
     * @param speedMetersPerSecond speed in m/s
     * @param distance distance in meters
     */
    public WorkoutGeoPoint(int latitudeE6, int longitudeE6, double altitude, boolean hasAltitude,
        float speedMetersPerSecond, double distance, double millisecondsInWorkout,
        double totalDistance, float course, long timestamp) {
        this.latitudeE6 = latitudeE6;
        this.longitudeE6 = longitudeE6;
        this.latLng = new LatLng(latitudeE6 * 1E-6, longitudeE6 * 1E-6);
        this.altitude = altitude;
        this.hasAltitude = hasAltitude;
        this.speedMetersPerSecond = speedMetersPerSecond;
        this.distance = distance;
        this.millisecondsInWorkout = (int) millisecondsInWorkout;
        this.totalDistance = totalDistance;
        this.course = course;
        this.timestamp = timestamp;
    }

    @VisibleForTesting
    public WorkoutGeoPoint(LatLng latLng, double altitude, boolean hasAltitude,
        float speedMetersPerSecond, double distance, double millisecondsInWorkout,
        double totalDistance, float course, long timestamp) {
        super();
        this.latLng = latLng;
        this.latitudeE6 = (int) (latLng.latitude * 1E6);
        this.longitudeE6 = (int) (latLng.longitude * 1E6);
        this.altitude = altitude;
        this.hasAltitude = hasAltitude;
        this.speedMetersPerSecond = speedMetersPerSecond;
        this.distance = distance;
        this.millisecondsInWorkout = (int) millisecondsInWorkout;
        this.totalDistance = totalDistance;
        this.course = course;
        this.timestamp = timestamp;
    }

    /**
     * @return the altitude in meters
     */
    public double getAltitude() {
        return altitude;
    }

    /**
     * @return the speed in m/s
     */
    public float getSpeedMetersPerSecond() {
        return speedMetersPerSecond;
    }

    /**
     * @return the distance in meters
     */
    public double getDistance() {
        return distance;
    }

    /**
     * @return the length of workout so far in milliseconds
     */
    public int getMillisecondsInWorkout() {
        return millisecondsInWorkout;
    }

    public double getTotalDistance() {
        return totalDistance;
    }

    public float getCourse() {
        return this.course;
    }

    public boolean hasAltitude() {
        return hasAltitude;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public LatLng getLatLng() {
        // If this object is initialized from Gson then latLng is null.
        if (latLng == null) {
            latLng = new LatLng(latitudeE6 * 1E-6, longitudeE6 * 1E-6);
        }
        return latLng;
    }

    public int getLatitudeE6() {
        return (int) (latLng.latitude * 1E6);
    }

    public int getLongitudeE6() {
        return (int) (latLng.longitude * 1E6);
    }

    /**
     * @return the latitude of this GeoPoint in degrees
     */
    public double getLatitude() {
        return latLng.latitude;
    }

    /**
     * @return the longitude of this GeoPoint in degrees
     */
    public double getLongitude() {
        return latLng.longitude;
    }

    public boolean isSameLocation(WorkoutGeoPoint other) {
        return latLng.equals(other.latLng);
    }

    @Override
    public String toString() {
        return String.format(Locale.US,
            "WorkoutGeoPoint: [latitude=%f, longitude=%f, altitude=%.2f, hasAltitude=%s, "
                + "speedMetersPerSecond=%.2f, distance=%.2f, "
                + "millisecondsInWorkout=%d, totalDistance=%.2f, course=%.2f timestamp=%d",
            latLng.latitude, latLng.longitude, altitude, hasAltitude, speedMetersPerSecond,
            distance, millisecondsInWorkout, totalDistance, course, timestamp);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeParcelable(latLng, 0);
        dest.writeDouble(altitude);
        dest.writeByte((byte) (hasAltitude ? 1 : 0));
        dest.writeFloat(speedMetersPerSecond);
        dest.writeDouble(distance);
        dest.writeInt(millisecondsInWorkout);
        dest.writeDouble(totalDistance);
        dest.writeFloat(course);
        dest.writeLong(timestamp);
    }

    public static final Parcelable.Creator<WorkoutGeoPoint> CREATOR =
        new Creator<WorkoutGeoPoint>() {
            @Override
            public WorkoutGeoPoint[] newArray(int size) {
                return new WorkoutGeoPoint[size];
            }

            @Override
            public WorkoutGeoPoint createFromParcel(Parcel source) {
                LatLng latLng = source.readParcelable(LatLng.class.getClassLoader());
                double altitude = source.readDouble();
                boolean hasAltitude = source.readByte() == 1;
                float speedMetersPerSecond = source.readFloat();
                double distance = source.readDouble();
                int millisecondsInWorkout = source.readInt();
                double totalDistance = source.readDouble();
                float course = source.readFloat();
                long timestamp = source.readLong();
                return new WorkoutGeoPoint(latLng, altitude, hasAltitude, speedMetersPerSecond,
                    distance, millisecondsInWorkout, totalDistance, course, timestamp);
            }
        };
}
