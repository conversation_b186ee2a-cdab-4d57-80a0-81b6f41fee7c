package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import android.provider.BaseColumns
import androidx.core.database.getIntOrNull
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.data.source.local.RankingDao
import com.stt.android.data.source.local.ranking.LocalRanking
import com.stt.android.db.containsTable
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.rx2.rxCompletable
import timber.log.Timber
import java.sql.SQLException

private const val RANKINGS_TABLE = "ranking"
private const val ID = BaseColumns._ID
private const val WORKOUT_KEY = "workoutKey"
private const val RANKING_TYPE = "rankingType"
private const val RANKING = "ranking"
private const val NUMBER_OF_WORKOUTS = "numberOfWorkouts"

class DatabaseUpgrade40To41Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper,
    private val rankingDao: RankingDao
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        if (db.containsTable(RANKINGS_TABLE)) {
            Timber.d("Migrating rankings from old db to room db")
            db.apply {
                query(RANKINGS_TABLE, null, null, null, null, null, null).use { cursor ->
                    val rankings = mutableListOf<LocalRanking>()
                    if (cursor.moveToFirst()) {
                        Timber.v("Loaded rankings from old database table")
                        do {
                            val localRanking = LocalRanking(
                                id = cursor.getString(cursor.getColumnIndexOrThrow(ID)),
                                key = cursor.getString(cursor.getColumnIndexOrThrow(WORKOUT_KEY)),
                                type = cursor.getString(cursor.getColumnIndexOrThrow(RANKING_TYPE)),
                                ranking = cursor.getIntOrNull(cursor.getColumnIndexOrThrow(RANKING)),
                                numberOfWorkouts = cursor.getIntOrNull(cursor.getColumnIndexOrThrow(NUMBER_OF_WORKOUTS))
                            )
                            Timber.v("Processing ranking: %s", localRanking)
                            rankings += localRanking
                        } while (cursor.moveToNext())
                        rxCompletable {
                            rankingDao.insert(rankings)
                        }.subscribeOn(Schedulers.io())
                            .blockingAwait()
                        Timber.d("Successfully migrated rankings to Room db")
                    }
                }
                Timber.d("Dropping old rankings table and index")
                execSQL("DROP TABLE IF EXISTS $RANKINGS_TABLE")
                // TODO drop index also
            }
        }
    }
}
