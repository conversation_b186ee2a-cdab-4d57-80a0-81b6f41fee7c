package com.stt.android.domain.database

import android.content.SharedPreferences
import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao
import com.stt.android.db.containsTable
import com.stt.android.db.getDoubleOrNull
import com.stt.android.db.getFloatOrNull
import com.stt.android.db.getInt
import com.stt.android.db.getIntOrNull
import com.stt.android.db.getLongOrNull
import com.stt.android.db.getStringOrNull
import com.stt.android.utils.STTConstants
import io.reactivex.Completable
import io.reactivex.Observable
import io.reactivex.schedulers.Schedulers
import timber.log.Timber
import java.sql.SQLException
import androidx.core.content.edit

class DatabaseUpgrade49To50Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper,
    private val summaryExtensionDao: SummaryExtensionDao,
    private val sharedPreferences: SharedPreferences
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {

    @Throws(SQLException::class)
    override fun upgrade() {
        if (db.containsTable(SUMMARY_EXTENSION_TABLE_NAME)) {
            Timber.d("Migrating `SummaryExtension`s from old db to room db")
            db.query(
                SUMMARY_EXTENSION_TABLE_NAME,
                null,
                null,
                null,
                null,
                null,
                null
            ).use { cursor ->
                cursor.apply {
                    val summaryExtensions = mutableListOf<LocalSummaryExtension>()
                    val total = cursor.count
                    var index = 0
                    if (cursor.moveToFirst()) {
                        Timber.v("Loaded `SummaryExtension`s from old database table")
                        do {
                            val localSummaryExtension = LocalSummaryExtension(
                                workoutId = getInt(DbFields.WORKOUT_ID),
                                pte = getFloatOrNull(DbFields.PTE)?.takeIf { it > 0f },
                                feeling = getIntOrNull(DbFields.FEELING)?.takeIf { it > 0 },
                                avgTemperature = getFloatOrNull(DbFields.AVG_TEMPERATURE)?.takeIf { it > 0f },
                                peakEpoc = getFloatOrNull(DbFields.PEAK_EPOC)?.takeIf { it > 0f },
                                avgPower = getFloatOrNull(DbFields.AVG_POWER)?.takeIf { it > 0f },
                                avgCadence = getFloatOrNull(DbFields.AVG_CADENCE)?.takeIf { it > 0f },
                                avgSpeed = getFloatOrNull(DbFields.AVG_SPEED)?.takeIf { it > 0f },
                                ascentTime = getFloatOrNull(DbFields.ASCENT_TIME)?.takeIf { it > 0f },
                                descentTime = getFloatOrNull(DbFields.DESCENT_TIME)?.takeIf { it > 0f },
                                performanceLevel = getFloatOrNull(DbFields.PERFORMANCE_LEVEL)?.takeIf { it > 0f },
                                recoveryTime = getLongOrNull(DbFields.RECOVERY_TIME)?.takeIf { it > 0 },
                                ascent = getDoubleOrNull(DbFields.ASCENT)?.takeIf { it > 0.0 },
                                descent = getDoubleOrNull(DbFields.DESCENT)?.takeIf { it > 0.0 },
                                deviceHardwareVersion = getStringOrNull(DbFields.DEVICE_HARDWARE_VERSION),
                                deviceSoftwareVersion = getStringOrNull(DbFields.DEVICE_SOFTWARE_VERSION),
                                productType = null, // not supported in previous db
                                displayName = null, // not supported in previous db
                                deviceName = getStringOrNull(DbFields.DEVICE_NAME),
                                deviceSerialNumber = getStringOrNull(DbFields.DEVICE_SERIAL_NUMBER),
                                deviceManufacturer = getStringOrNull(DbFields.DEVICE_MANUFACTURER),
                                exerciseId = getStringOrNull(DbFields.EXERCISE_ID),
                                zapps = listOf(), // not supported in previous db, will be populated by SummaryExtensionUpdateWithZappsUseCase
                                maxCadence = null,
                                repetitionCount = null,
                                avgStrideLength = null,
                                fatConsumption = null,
                                carbohydrateConsumption = null,
                                avgGroundContactTime = null,
                                avgVerticalOscillation = null,
                                avgLeftGroundContactBalance = null,
                                avgRightGroundContactBalance = null,
                                lacticThHr = null,
                                lacticThPace = null,
                                avgAscentSpeed = null,
                                maxAscentSpeed = null,
                                avgDescentSpeed = null,
                                maxDescentSpeed = null,
                                avgDistancePerStroke = null
                            )
                            Timber.v("Processing SummaryExtension: ${index++}/$total")
                            summaryExtensions += localSummaryExtension
                        } while (cursor.moveToNext())

                        // perform migration with list and fallback to 1-by-1
                        Completable.fromAction { summaryExtensionDao.insertList(summaryExtensions) }
                            .onErrorResumeNext { errorWithList ->
                                Timber.w(errorWithList, "Error migrating batch of SummaryExtension, fallback to one-by-one")
                                Observable.fromIterable(summaryExtensions)
                                    .doOnNext { summaryExtension ->
                                        runCatching {
                                            summaryExtensionDao.insert(summaryExtension)
                                        }.onFailure { e ->
                                            Timber.w(e, "Error migrating SummaryExtension: $summaryExtension")
                                        }
                                    }
                                    .toList()
                                    .ignoreElement()
                                    .subscribeOn(Schedulers.io())
                            }
                            .subscribeOn(Schedulers.io())
                            .blockingAwait()
                        Timber.d("Successfully migrated SummaryExtension to Room db")
                    }
                }
            }
            Timber.d("Dropping old SummaryExtension table")
            db.execSQL("DROP TABLE IF EXISTS $SUMMARY_EXTENSION_TABLE_NAME")
            // we cannot directly schedule something with WorkManager at this point because DB has to be ready first
            sharedPreferences.edit {
                putBoolean(
                    STTConstants.DefaultPreferences.KEY_SCHEDULE_SUMMARY_EXTENSION_UPDATE_WITH_ZAPPS,
                    true
                )
            }
        }
    }

    object DbFields {
        const val WORKOUT_ID = "workoutId"
        const val PTE = "pte"
        const val FEELING = "feeling"
        const val AVG_TEMPERATURE = "avgTemperature"
        const val PEAK_EPOC = "peakEpoc"
        const val AVG_POWER = "avgPower"
        const val AVG_CADENCE = "avgCadence"
        const val AVG_SPEED = "avgSpeed"
        const val ASCENT_TIME = "ascentTime"
        const val DESCENT_TIME = "descentTime"
        const val PERFORMANCE_LEVEL = "performanceLevel"
        const val RECOVERY_TIME = "recoveryTime"
        const val ASCENT = "ascent"
        const val DESCENT = "descent"
        const val DEVICE_HARDWARE_VERSION = "deviceHardwareVersion"
        const val DEVICE_SOFTWARE_VERSION = "deviceSoftwareVersion"
        const val DEVICE_NAME = "deviceName"
        const val DEVICE_SERIAL_NUMBER = "deviceSerialNumber"
        const val DEVICE_MANUFACTURER = "deviceManufacturer"
        const val EXERCISE_ID = "exerciseId"
    }

    companion object {
        const val SUMMARY_EXTENSION_TABLE_NAME = "summaryExtension"
    }
}
