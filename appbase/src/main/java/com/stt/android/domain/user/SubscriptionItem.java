package com.stt.android.domain.user;

/**
 * Represents a subscription owned by the user.
 * Basically it's a wrapper around {@link UserSubscription}.
 */
public class SubscriptionItem {
    private final int id;
    private final UserSubscription userSubscription;

    public SubscriptionItem(UserSubscription userSubscription) {
        this.id = 0;
        this.userSubscription = userSubscription;
    }

    public SubscriptionItem(int id, UserSubscription userSubscription) {
        this.id = id;
        this.userSubscription = userSubscription;
    }

    public int getId() {
        return id;
    }

    public UserSubscription getUserSubscription() {
        return userSubscription;
    }
}
