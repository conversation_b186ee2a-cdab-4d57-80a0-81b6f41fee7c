package com.stt.android.domain.user;

import android.text.format.DateUtils;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import com.stt.android.domain.goaldefinition.GoalDefinition;
import com.stt.android.domain.workout.ActivityType;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * This class represents status of a goal.
 */
public class Goal {
    @IntDef({STATUS_ONGOING, STATUS_REACHED, STATUS_FAILED})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Status {
    }

    public static final int STATUS_ONGOING = 0;
    public static final int STATUS_REACHED = 1;
    public static final int STATUS_FAILED = 2;

    public final GoalDefinition goalDefinition;
    private final long startTime;
    private final long endTime;

    /**
     * How much target has been achieved.
     */
    public final int achieved;

    /**
     * Total time for workouts, only valid for "number of workout" goals.
     */
    public final int totalTime;

    @NonNull
    public final List<ActivityType> activities;

    @NonNull
    public final List<Integer> workoutIds;

    /**
     * Used by {@link GoalDefinition} only.
     */
    public Goal(@NonNull GoalDefinition goalDefinition, long startTime, long endTime) {
        this.goalDefinition = goalDefinition;
        this.startTime = startTime;
        this.endTime = endTime;
        achieved = 0;
        totalTime = 0;
        activities = Collections.emptyList();
        workoutIds = Collections.emptyList();
    }

    private Goal(GoalDefinition goalDefinition, long startTime, long endTime, int achieved,
                 int totalTime, List<ActivityType> activities, List<Integer> workoutIds) {
        this.goalDefinition = goalDefinition;
        this.startTime = startTime;
        this.endTime = endTime;
        this.achieved = achieved;
        this.totalTime = totalTime;
        this.activities = Collections.unmodifiableList(activities);
        this.workoutIds = Collections.unmodifiableList(workoutIds);
    }

    public Goal update(int achieved, int totalTime, List<ActivityType> activities, List<Integer> workoutIds) {
        return new Goal(goalDefinition, startTime, endTime, achieved, totalTime, activities, workoutIds);
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public int getAchievedPercentage() {
        int target = goalDefinition.getTarget();
        return target == 0 ? 100 : Math.min(100, 100 * achieved / target);
    }

    public int getRemainingDays() {
        // if the goal finishes today, we consider the remaining day as 1
        return (int) ((endTime - System.currentTimeMillis()) / DateUtils.DAY_IN_MILLIS) + 1;
    }

    @Status
    public int getStatus() {
        if (getAchievedPercentage() >= 100) {
            return STATUS_REACHED;
        }
        return (endTime > System.currentTimeMillis()) ? STATUS_ONGOING : STATUS_FAILED;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Goal goal = (Goal) o;
        return startTime == goal.startTime
            && endTime == goal.endTime
            && achieved == goal.achieved
            && totalTime == goal.totalTime
            && Objects.equals(goalDefinition, goal.goalDefinition)
            && Objects.equals(activities, goal.activities)
            && Objects.equals(workoutIds, goal.workoutIds);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            goalDefinition,
            startTime,
            endTime,
            achieved,
            totalTime,
            activities,
            workoutIds
        );
    }
}
