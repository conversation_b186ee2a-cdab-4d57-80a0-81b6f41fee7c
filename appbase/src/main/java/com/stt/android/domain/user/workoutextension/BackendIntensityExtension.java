package com.stt.android.domain.user.workoutextension;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;
import com.soy.algorithms.intensity.IntensityZones;
import com.soy.algorithms.intensity.IntensityZonesData;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;

public class BackendIntensityExtension extends BackendWorkoutExtension {
    public static final String TYPE = "IntensityExtension";

    @Nullable
    @SerializedName("zones")
    private BackendIntensityExtensionIntensityZones zones;
    // TODO add physiologicalThresholds once watch supports it

    public static class BackendIntensityExtensionIntensityZones {

        @Nullable
        @SerializedName("heartRate")
        final BackendIntensityExtensionZones heartRate;

        @Nullable
        @SerializedName("speed")
        final BackendIntensityExtensionZones speed;

        @Nullable
        @SerializedName("power")
        final BackendIntensityExtensionZones power;

        //no args constructor for GSON
        public BackendIntensityExtensionIntensityZones() {
            this.heartRate = null;
            this.speed = null;
            this.power = null;
        }

        public BackendIntensityExtensionIntensityZones(@NonNull IntensityExtension intensityExtension) {
            IntensityZones intensityZones = intensityExtension.getIntensityZones();
            heartRate = intensityZones.getHr() != null ?
                    new BackendIntensityExtensionZones(intensityZones.getHr().hzToBpm()) : null;
            speed = intensityZones.getSpeed() != null ?
                    new BackendIntensityExtensionZones(intensityZones.getSpeed()) : null;
            power = intensityZones.getPower() != null ?
                    new BackendIntensityExtensionZones(intensityZones.getPower()) : null;
        }
    }



    public static class BackendIntensityExtensionZones {

        @SerializedName("zone1")
        final BackendIntensityExtensionZone zone1;

        @SerializedName("zone2")
        final BackendIntensityExtensionZone zone2;

        @SerializedName("zone3")
        final BackendIntensityExtensionZone zone3;

        @SerializedName("zone4")
        final BackendIntensityExtensionZone zone4;

        @SerializedName("zone5")
        final BackendIntensityExtensionZone zone5;

        //no args constructor for GSON
        public BackendIntensityExtensionZones() {
            this.zone1 = new BackendIntensityExtensionZone();
            this.zone2 = new BackendIntensityExtensionZone();
            this.zone3 = new BackendIntensityExtensionZone();
            this.zone4 = new BackendIntensityExtensionZone();
            this.zone5 = new BackendIntensityExtensionZone();
        }

        public BackendIntensityExtensionZones(@NonNull IntensityZonesData zone) {
            this.zone1 = new BackendIntensityExtensionZone(
                    zone.getZone1Duration(), 0f);
            this.zone2 = new BackendIntensityExtensionZone(
                    zone.getZone2Duration(), zone.getZone2LowerLimit());
            this.zone3 = new BackendIntensityExtensionZone(
                    zone.getZone3Duration(), zone.getZone3LowerLimit());
            this.zone4 = new BackendIntensityExtensionZone(
                    zone.getZone4Duration(), zone.getZone4LowerLimit());
            this.zone5 = new BackendIntensityExtensionZone(
                    zone.getZone5Duration(), zone.getZone5LowerLimit());
        }
    }

    public static class BackendIntensityExtensionZone {

        @SerializedName("totalTime")
        final float totalTime;

        @SerializedName("lowerLimit")
        final float lowerLimit;

        //no args constructor for GSON
        public BackendIntensityExtensionZone() {
            this.totalTime = 0f;
            this.lowerLimit = 0f;
        }

        public BackendIntensityExtensionZone(float totalTime, float lowerLimit) {
            this.totalTime = totalTime;
            this.lowerLimit = lowerLimit;
        }
    }

    /**
     * No args constructor to make Gson safe. See:https://stackoverflow
     * .com/questions/18645050/is-default-no-args-constructor-mandatory-for-gson
     */
    protected BackendIntensityExtension() {
        super(TYPE);
    }

    public BackendIntensityExtension(@NonNull IntensityExtension extension) {
        super(TYPE);

        zones = new BackendIntensityExtensionIntensityZones(extension);
    }

    @NonNull
    @Override
    public WorkoutExtension toWorkoutExtension(int workoutId) {
        final IntensityZonesData heartIntensityZone;
        final IntensityZonesData speedIntensityZone;
        final IntensityZonesData powerIntensityZone;

        if (this.zones != null) {
            if (this.zones.heartRate != null) {
                heartIntensityZone =
                    new IntensityZonesData(
                        this.zones.heartRate.zone1.totalTime,
                        this.zones.heartRate.zone2.totalTime,
                        this.zones.heartRate.zone3.totalTime,
                        this.zones.heartRate.zone4.totalTime,
                        this.zones.heartRate.zone5.totalTime,
                        this.zones.heartRate.zone2.lowerLimit,
                        this.zones.heartRate.zone3.lowerLimit,
                        this.zones.heartRate.zone4.lowerLimit,
                        this.zones.heartRate.zone5.lowerLimit
                    ).bpmToHz();
            } else {
                heartIntensityZone = null;
            }
            if (this.zones.speed != null) {
                speedIntensityZone =
                    new IntensityZonesData(
                        this.zones.speed.zone1.totalTime,
                        this.zones.speed.zone2.totalTime,
                        this.zones.speed.zone3.totalTime,
                        this.zones.speed.zone4.totalTime,
                        this.zones.speed.zone5.totalTime,
                        this.zones.speed.zone2.lowerLimit,
                        this.zones.speed.zone3.lowerLimit,
                        this.zones.speed.zone4.lowerLimit,
                        this.zones.speed.zone5.lowerLimit
                    );
            } else {
                speedIntensityZone = null;
            }
            if (this.zones.power != null) {
                powerIntensityZone =
                    new IntensityZonesData(
                        this.zones.power.zone1.totalTime,
                        this.zones.power.zone2.totalTime,
                        this.zones.power.zone3.totalTime,
                        this.zones.power.zone4.totalTime,
                        this.zones.power.zone5.totalTime,
                        this.zones.power.zone2.lowerLimit,
                        this.zones.power.zone3.lowerLimit,
                        this.zones.power.zone4.lowerLimit,
                        this.zones.power.zone5.lowerLimit
                    );
            } else {
                powerIntensityZone = null;
            }
        } else {
            heartIntensityZone = null;
            speedIntensityZone = null;
            powerIntensityZone = null;
        }

        return new IntensityExtension(
            workoutId,
            new IntensityZones(heartIntensityZone, speedIntensityZone, powerIntensityZone)
        );
    }
}
