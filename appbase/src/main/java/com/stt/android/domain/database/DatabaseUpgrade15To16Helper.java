package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.database.deprecated.OldRouteTable;
import com.stt.android.domain.user.LegacyUser;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import com.stt.android.domain.user.Reaction;
import com.stt.android.domain.user.ReactionSummary;
import java.sql.SQLException;

public class DatabaseUpgrade15To16Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade15To16Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @SuppressWarnings("deprecation")
    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, LegacyUser.TABLE_NAME, LegacyUser.DbFields.PROFILE_IMAGE_KEY);

        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.KEY);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.VISIBILITY);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.AVERAGE_SPEED);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.OWNER_USER_NAME);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.CENTER_POINT);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.STOP_POINT);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.LOCALLY_CHANGED);
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.DELETED);

        TableUtils.createTableIfNotExists(connectionSource, ReactionSummary.class);
        TableUtils.createTableIfNotExists(connectionSource, Reaction.class);
        DatabaseHelper.addColumnIfNotExist(db, LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.REACTION_COUNT);
    }
}
