package com.stt.android.domain;

import com.google.gson.annotations.SerializedName;
import com.stt.android.exceptions.BackendException;
import java.util.Map;
import timber.log.Timber;

public class ResponseWrapper<I> {
    @SerializedName("error")
    private final ErrorWrapper error;
    @SerializedName("metadata")
    private final Map<String, String> metadata;
    @SerializedName("payload")
    private final I payload;

    public ResponseWrapper(ErrorWrapper error, Map<String, String> metadata, I payload) {
        this.error = error;
        this.metadata = metadata;
        this.payload = payload;
    }

    /**
     * @param response
     * @param url
     * @return the response payload or empty list if payload is null.
     * @throws BackendException if there was an error in the response itself
     */
    public static <E> E checkResponse(ResponseWrapper<E> response,
                                      String url) throws BackendException {
        if (response.getError() != null) {
            Timber.e("Error in request %s", url);
            throw new BackendException(response.getError());
        }
        E payload = response.getPayload();
        if (payload == null) {
            String msg = "WTF: Null payload in response " + response + " returned by " + url;
            Timber.e(msg);
            throw new BackendException(msg);
        }
        return payload;
    }

    public STTErrorCodes getError() {
        return error == null ? null : STTErrorCodes.valueOf(error.code);
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    public String getMetadata(String key, String defaultValue) {
        if (metadata == null) {
            return defaultValue;
        }
        if (!metadata.containsKey(key)) {
            return defaultValue;
        }
        return metadata.get(key);
    }

    public long getLastModified() {
        String lastModifiedString = getMetadata("until", null);
        if (lastModifiedString == null) {
            return 0L;
        }
        return Long.valueOf(lastModifiedString);
    }

    /**
     * @return the payload
     */
    public I getPayload() {
        return payload;
    }

    private static class ErrorWrapper {
        @SerializedName("code")
        private final int code;
        @SerializedName("description")
        private final String description;

        private ErrorWrapper(int code, String description) {
            this.code = code;
            this.description = description;
        }
    }
}
