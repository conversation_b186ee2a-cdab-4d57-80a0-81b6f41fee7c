package com.stt.android.domain.user.workout.tss

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.source.local.workout.tss.LocalTSS
import com.stt.android.data.source.local.workout.tss.LocalTSSCalculationMethod
import com.stt.android.domain.workouts.tss.TSS

fun TSS?.toLocal(): LocalTSS? = this?.run {
    LocalTSS(
        trainingStressScore = trainingStressScore,
        calculationMethod = calculationMethod.toLocal(),
        intensityFactor = intensityFactor,
        normalizedPower = normalizedPower,
        averageGradeAdjustedPace = averageGradeAdjustedPace,
    )
}

fun TSSCalculationMethod.toLocal(): LocalTSSCalculationMethod = when (this) {
    TSSCalculationMethod.POWER -> LocalTSSCalculationMethod.POWER
    TSSCalculationMethod.PACE -> LocalTSSCalculationMethod.PACE
    TSSCalculationMethod.HR -> LocalTSSCalculationMethod.HR
    TSSCalculationMethod.SWIM_PACE -> LocalTSSCalculationMethod.SWIM_PACE
    TSSCalculationMethod.MET -> LocalTSSCalculationMethod.MET
    TSSCalculationMethod.MANUAL -> LocalTSSCalculationMethod.MANUAL
    TSSCalculationMethod.DYNAMIC_DFA -> LocalTSSCalculationMethod.DYNAMIC_DFA
}

fun BackendTSS?.toDomain(): TSS? = this?.run {
    TSS(
        trainingStressScore = trainingStressScore,
        calculationMethod = calculationMethod.toDomain(),
        intensityFactor = intensityFactor,
        normalizedPower = normalizedPower,
        averageGradeAdjustedPace = averageGradeAdjustedPace,
    )
}

private fun BackendTSSCalculationMethod.toDomain(): TSSCalculationMethod = when (this) {
    BackendTSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    BackendTSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    BackendTSSCalculationMethod.HR -> TSSCalculationMethod.HR
    BackendTSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    BackendTSSCalculationMethod.MET -> TSSCalculationMethod.MET
    BackendTSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    BackendTSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}

fun LocalTSS.toDomain(): TSS = TSS(
    trainingStressScore = trainingStressScore,
    calculationMethod = calculationMethod.toDomain(),
    intensityFactor = intensityFactor,
    normalizedPower = normalizedPower,
    averageGradeAdjustedPace = averageGradeAdjustedPace
)

fun LocalTSSCalculationMethod.toDomain(): TSSCalculationMethod = when (this) {
    LocalTSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    LocalTSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    LocalTSSCalculationMethod.HR -> TSSCalculationMethod.HR
    LocalTSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    LocalTSSCalculationMethod.MET -> TSSCalculationMethod.MET
    LocalTSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    LocalTSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}

fun TSSCalculationMethod.toAlg(): TSSCalculationMethod = when (this) {
    TSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    TSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    TSSCalculationMethod.HR -> TSSCalculationMethod.HR
    TSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    TSSCalculationMethod.MET -> TSSCalculationMethod.MET
    TSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    TSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}

fun TSSCalculationMethod.toDomain(): TSSCalculationMethod = when (this) {
    TSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    TSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    TSSCalculationMethod.HR -> TSSCalculationMethod.HR
    TSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    TSSCalculationMethod.MET -> TSSCalculationMethod.MET
    TSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    TSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}
