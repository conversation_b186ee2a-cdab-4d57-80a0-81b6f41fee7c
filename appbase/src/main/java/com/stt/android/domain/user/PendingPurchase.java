package com.stt.android.domain.user;

import com.stt.android.billing.Purchase;

/**
 * Represents a purchase pending owned by the user.
 * Basically it's a wrapper around {@link com.stt.android.billing.Purchase}
 * More details at: http://developer.android.com/google/play/billing/billing_reference.html#getBuyIntent
 */
public class PendingPurchase {
    private final String id;
    private final Purchase purchase;

    public PendingPurchase(Purchase purchase) {
        this.id = purchase.getToken();
        this.purchase = purchase;
    }

    public String getId() {
        return id;
    }

    public Purchase getPurchase() {
        return purchase;
    }
}
