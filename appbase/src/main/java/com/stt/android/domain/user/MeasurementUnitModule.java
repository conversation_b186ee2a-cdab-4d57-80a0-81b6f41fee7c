package com.stt.android.domain.user;

import com.stt.android.controllers.UserSettingsController;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public class MeasurementUnitModule {
    @Provides
    static MeasurementUnit provideMeasurementUnit(UserSettingsController controller) {
        return controller.getSettings().getMeasurementUnit();
    }
}
