package com.stt.android.domain.user;

/**
 * Only used for ancient OrmLite migrations and to refer to column names for Ormlite->Room migration.
 * Use {@link com.stt.android.domain.user.User} for everything else.
 */
@Deprecated
public class LegacyUser {
    public static final String ANONYMOUS_USERNAME = "anonymous";
    public static final String TABLE_NAME = "user";

    public abstract static class DbFields {
        public static final String ID = "id";
        public static final String KEY = "key";
        public static final String USERNAME = "username";
        public static final String SESSION = "session";
        public static final String WEBSITE = "website";
        public static final String CITY = "city";
        public static final String COUNTRY = "country";
        public static final String PROFILE_IMAGE_URL = "profileImageUrl";
        public static final String PROFILE_IMAGE_KEY = "profileImageKey";
        public static final String REAL_NAME = "realName";
        public static final String FRIEND_ID = "friendId";
        public static final String FOLLOW_MODEL = "followModel";
        public static final String DESCRIPTION = "description";
        public static final String FIELD_TESTER = "fieldtester";
    }
}
