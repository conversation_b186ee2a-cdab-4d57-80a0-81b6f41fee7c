package com.stt.android.domain.sync

/**
 * Used as a parameter for <PERSON><PERSON><PERSON><PERSON>questHand<PERSON> to indicate what kinds of syncs should be done.
 * [SyncRequest] has helper methods for creating these bitmasks.
 * First bit - push
 * Second bit - pull own workouts
 * Third bit - pull followees
 * Fourth bit - pull followees workouts
 * Fifth bit - pull feed
 */
typealias SyncRequestBitmask = Int

fun SyncRequestBitmask.merge(with: SyncRequestBitmask) = this or with

val SyncRequestBitmask.push: <PERSON>olean
    get() = this and SyncRequest.push() != 0

val SyncRequestBitmask.pullOwnWorkouts: <PERSON>olean
    get() = this and SyncRequest.pullOwnWorkouts() != 0

val SyncRequestBitmask.pullFollowees: Boolean
    get() = this and SyncRequest.pullFollowees() != 0

val SyncRequestBitmask.pullFolloweesWorkouts: Boolean
    get() = this and SyncRequest.pullFolloweesWorkouts() != 0

val SyncRequestBitmask.pullFeed: Boolean
    get() = this and SyncRequest.pullFeed() != 0

object SyncRequest {
    @JvmStatic
    fun push(): SyncRequestBitmask = 0x1

    @JvmStatic
    fun pullOwnWorkouts(): SyncRequestBitmask = 0x2

    @JvmStatic
    fun pullFollowees(): SyncRequestBitmask = 0x4

    @JvmStatic
    fun pullFolloweesWorkouts(): SyncRequestBitmask = 0x8

    @JvmStatic
    fun pullFeed(): SyncRequestBitmask = 0x10

    @JvmStatic
    fun pullAll(): SyncRequestBitmask = pullOwnWorkouts() or
        pullFollowees() or
        pullFolloweesWorkouts() or
        pullFeed()

    @JvmStatic
    fun all(): SyncRequestBitmask = push() or
        pullOwnWorkouts() or
        pullFollowees() or
        pullFolloweesWorkouts() or
        pullFeed()

    @JvmStatic
    fun create(
        push: Boolean = false,
        pullOwnWorkouts: Boolean = false,
        pullFollowees: Boolean = false,
        pullFolloweesWorkouts: Boolean = false,
        pullFeed: Boolean = false
    ): SyncRequestBitmask = (if (push) push() else 0) or
        (if (pullOwnWorkouts) pullOwnWorkouts() else 0) or
        (if (pullFollowees) pullFollowees() else 0) or
        (if (pullFolloweesWorkouts) pullFolloweesWorkouts() else 0) or
        (if (pullFeed) pullFeed() else 0)
}
