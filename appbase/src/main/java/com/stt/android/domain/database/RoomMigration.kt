package com.stt.android.domain.database

import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.stt.android.db.containsTable
import timber.log.Timber

/**
 * Base class for all Room migrations.
 *
 * Extend this class to migrate a table from the old database to Room database.
 *
 * @param DataEntity The Room data entity
 * @param Dao The Room Dao that handles [DataEntity]
 */
abstract class RoomMigration<DataEntity, Dao>(
    private val db: SQLiteDatabase,
    private val dao: Dao,
    private val tableName: String
) {
    fun migrateToRoom() {
        if (db.containsTable(tableName)) {
            Timber.d("Migrating $tableName from old db to room db")
            db.apply {
                query(tableName, null, null, null, null, null, null).use { cursor ->
                    val dataEntities = mutableListOf<DataEntity>()
                    if (cursor.moveToFirst()) {
                        Timber.v("Loaded data from old database table")
                        do {
                            dataEntities += mapToDataEntity(cursor)
                        } while (cursor.moveToNext())
                        insertDataEntities(dao, dataEntities)
                        Timber.d("Successfully migrated $tableName to Room db")
                    }
                }
                Timber.d("Dropping old $tableName table")
                execSQL("DROP TABLE IF EXISTS $tableName")
            }
        }
    }

    /**
     * Implement this method to insert the passed [dataEntities] to Room
     * database table [tableName].
     *
     * @param dao The DAO that SQL operations of [dataEntities]
     * @param dataEntities List of [DataEntity] to be inserted to [tableName]
     */
    abstract fun insertDataEntities(dao: Dao, dataEntities: List<DataEntity>)

    /**
     * Implement this method to map the data from [cursor] and return a new instance of
     * [DataEntity]
     *
     * @param cursor Cursor that references a row from the old database table [tableName]
     */
    abstract fun mapToDataEntity(cursor: Cursor): DataEntity
}
