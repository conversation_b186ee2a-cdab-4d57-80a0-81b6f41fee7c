package com.stt.android.domain.sync

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.notifications.FetchAndStoreNotificationsUseCase
import com.stt.android.domain.user.followees.FetchAndStoreFolloweesUseCase
import com.stt.android.domain.workouts.FetchAndStoreFolloweesWorkoutsUseCase
import com.stt.android.domain.workouts.FetchAndStoreOwnWorkoutsUseCase
import com.stt.android.domain.workouts.PushLocallyChangedWorkoutsUseCase
import com.stt.android.utils.FlavorUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.isActive
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages synchronization of workouts and feed data in a way that only one synchronization batch
 * is happening at once, and tries to reduce duplicated work by merging batches waiting to be run
 */
@Singleton
class SyncRequestHandler @Inject constructor(
    private val pushLocallyChangedWorkoutsUseCase: PushLocallyChangedWorkoutsUseCase,
    private val fetchAndStoreOwnWorkoutsUseCase: FetchAndStoreOwnWorkoutsUseCase,
    private val fetchAndStoreFolloweesUseCase: FetchAndStoreFolloweesUseCase,
    private val fetchAndStoreFolloweesWorkoutsUseCase: FetchAndStoreFolloweesWorkoutsUseCase,
    private val fetchAndStoreNotificationsUseCase: FetchAndStoreNotificationsUseCase,
    dispatcherProvider: CoroutinesDispatcherProvider,
) {
    private val scope = CoroutineScope(dispatcherProvider.io + SupervisorJob())

    private val jobEditingMutex = Mutex()
    private val jobRunningMutex = Mutex()

    private var pendingSyncJob: SyncJob? = null

    private var runningSyncJob: SyncJob? = null

    val isRunningSync: LiveData<Boolean>
        get() = _isRunningSync.distinctUntilChanged()
    private val _isRunningSync = MutableLiveData(false)

    suspend fun runRequestInQueue(request: SyncRequestBitmask): SyncResult {
        jobEditingMutex.lock()
        val currentlyPendingSyncJob = pendingSyncJob
        return if (currentlyPendingSyncJob != null) {
            Timber.d("Pending job in queue exists, merging and awaiting")
            currentlyPendingSyncJob.request = currentlyPendingSyncJob.request.merge(request)
            jobEditingMutex.unlock()
            awaitOrReturnCanceledResult(currentlyPendingSyncJob)
        } else {
            Timber.d("No pending jobs in queue, creating new one and awaiting")
            // lazy start due to pendingSyncJob that the coroutine is looking for is set
            // after the job has been created
            val newSyncJobAsyncResult = scope.async(start = CoroutineStart.LAZY) {
                jobRunningMutex.withLock {
                    jobEditingMutex.lock()
                    val syncJobToRun = pendingSyncJob
                    if (syncJobToRun != null) {
                        runningSyncJob = syncJobToRun
                        pendingSyncJob = null
                        jobEditingMutex.unlock()

                        updateIsRunningSync()

                        Timber.d("Starting to run SyncRequest: ${syncJobToRun.request}")
                        val result = runRequest(syncJobToRun.request, this)
                        Timber.d("Finished running SyncRequest: ${syncJobToRun.request}")

                        jobEditingMutex.withLock {
                            runningSyncJob = null
                        }

                        updateIsRunningSync()
                        result
                    } else {
                        jobEditingMutex.unlock()
                        updateIsRunningSync()
                        SyncResult(null, null, null, null, null, false)
                    }
                }
            }

            val newSyncJob = SyncJob(
                asyncResult = newSyncJobAsyncResult,
                request = request,
            )
            pendingSyncJob = newSyncJob
            jobEditingMutex.unlock()
            awaitOrReturnCanceledResult(newSyncJob)
        }
    }

    fun runRequestInQueueBlocking(request: SyncRequestBitmask): SyncResult = runBlocking {
        runRequestInQueue(request)
    }

    suspend fun cancelAll() {
        jobEditingMutex.withLock {
            pendingSyncJob?.asyncResult?.cancel()
            runningSyncJob?.asyncResult?.cancel()

            runningSyncJob = null
            pendingSyncJob = null
        }
    }

    fun cancelAllBlocking() = runBlocking {
        cancelAll()
    }

    private suspend fun runRequest(
        request: SyncRequestBitmask,
        coroutineScope: CoroutineScope
    ): SyncResult {
        val ownWorkoutResults = coroutineScope.async {
            val push = if (coroutineScope.isActive && request.push) {
                runSuspendCatching {
                    pushLocallyChangedWorkoutsUseCase()
                }.recover { e ->
                    Timber.w(e, "Error pushing locally changed workouts")
                    listOf(Result.failure(e))
                }.getOrNull()
            } else {
                null
            }

            val pull = if (coroutineScope.isActive && request.pullOwnWorkouts) {
                runSuspendCatching {
                    fetchAndStoreOwnWorkoutsUseCase()
                }.onFailure { e ->
                    Timber.w(e, "Error fetching and storing own workouts")
                }
            } else {
                null
            }

            push to pull
        }

        val followeesResults = coroutineScope.async {
            val followees = if (coroutineScope.isActive && request.pullFollowees) {
                runSuspendCatching {
                    fetchAndStoreFolloweesUseCase()
                }.onFailure { e ->
                    Timber.w(e, "Error fetching and storing followees")
                }
            } else {
                null
            }

            val followeesWorkouts = if (coroutineScope.isActive && request.pullFolloweesWorkouts) {
                runSuspendCatching {
                    fetchAndStoreFolloweesWorkoutsUseCase()
                }.onFailure { e ->
                    Timber.w(e, "Error fetching and storing followees' workouts")
                }
            } else {
                null
            }

            followees to followeesWorkouts
        }

        val pullFeedResult = coroutineScope.async {
            if (coroutineScope.isActive && request.pullFeed) {
                runSuspendCatching {
                    fetchAndStoreNotificationsUseCase(FlavorUtils.isSuuntoApp)
                }.onFailure { e ->
                    Timber.w(e, "Error fetching and storing feed metadata")
                }
            } else {
                null
            }
        }

        val (pushResult, pullOwnWorkoutsResult) = ownWorkoutResults.await()
        val (pullFolloweesResult, pullFolloweesWorkoutsResult) = followeesResults.await()

        return SyncResult(
            pushResult,
            pullOwnWorkoutsResult,
            pullFolloweesResult,
            pullFolloweesWorkoutsResult,
            pullFeedResult.await(),
            !coroutineScope.isActive
        )
    }

    private fun updateIsRunningSync() {
        val newValue = pendingSyncJob != null || runningSyncJob != null
        _isRunningSync.postValue(newValue)
    }

    private suspend fun awaitOrReturnCanceledResult(syncJob: SyncJob) = try {
        syncJob.asyncResult.await()
    } catch (e: Exception) {
        // could be restricted to CancelationException as everything is caught in runRequest,
        // but this works as a last fail-safe to make sure that awaiting the SyncJob$asyncResult never throws
        SyncResult(null, null, null, null, null, true)
    }

    private data class SyncJob(
        val asyncResult: Deferred<SyncResult>,
        var request: SyncRequestBitmask,
    )
}
