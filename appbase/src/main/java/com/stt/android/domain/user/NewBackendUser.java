package com.stt.android.domain.user;

import com.google.gson.annotations.SerializedName;

public class NewBackendUser {
    @SerializedName(value = "birthDate")
    private final long birthdate;
    @SerializedName(value = "email")
    private final String email;
    @SerializedName(value = "realName")
    private final String realname;
    @SerializedName(value = "username")
    private final String username;

    NewBackendUser(long birthdate, String email, String realname, String username) {
        this.birthdate = birthdate;
        this.email = email;
        this.realname = realname;
        this.username = username;
    }

    public long getBirthdate() {
        return birthdate;
    }

    public String getEmail() {
        return email;
    }

    public String getRealname() {
        return realname;
    }

    public String getUsername() {
        return username;
    }
}
