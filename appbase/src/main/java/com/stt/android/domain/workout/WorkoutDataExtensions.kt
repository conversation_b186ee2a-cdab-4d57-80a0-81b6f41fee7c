package com.stt.android.domain.workout

import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.tracker.event.Event

/**
 * Works for whole workout, but for multisports if you try to first call this and then filter out events with
 * timestamps not between multisport part's start and end timestamps the results won't have their x values adjusted
 * like most charts expect, and doing the adjustment at that point can be inaccurate. To get best results for
 * a multisport part, first use [filterEventsAndHrEvents] and then pass the result to [filterPausesAndAdjustMillisecondsInWorkout]
 */
val WorkoutData.heartRateEventsWithoutPauses: List<WorkoutHrEvent>
    get() = filterPausesAndAdjustMillisecondsInWorkout(
        events,
        heartRateEvents,
        null,
        listOf(Event.EventType.START),
        listOf(Event.EventType.STOP)
    )

fun WorkoutData.filterEventsAndHrEvents(multisportPartActivity: MultisportPartActivity?): Pair<List<Event>, List<WorkoutHrEvent>> =
    if (multisportPartActivity == null) {
        events to heartRateEvents
    } else {
        val partEvents =
            events.filter { it.realTime in multisportPartActivity.startTime..multisportPartActivity.stopTime }
        val partHrEvents =
            heartRateEvents.filter { it.timestamp in multisportPartActivity.startTime..multisportPartActivity.stopTime }
        partEvents to partHrEvents
    }

// NOTE: Pauses are excluded only if the workout binary contains recording events. See below.
fun filterPausesAndAdjustMillisecondsInWorkout(
    events: List<Event>,
    heartRateEvents: List<WorkoutHrEvent>,
    startTimeStamp: Long? = null,
    startEvents: List<Event.EventType> = listOf(Event.EventType.START, Event.EventType.CONTINUE),
    stopEvents: List<Event.EventType> = listOf(Event.EventType.STOP, Event.EventType.PAUSE)
): List<WorkoutHrEvent> {
    // Sanity check. Old workouts do not contain event data in the workout binary (workouts synced
    // with app version 4.10.2 or older. Not published yet, so exact version not known.)
    return if (events.none { startEvents.contains(it.type) } ||
        events.none { stopEvents.contains(it.type) }
    ) {
        if (startTimeStamp != null) {
            heartRateEvents.map {
                WorkoutHrEvent(it.timestamp, it.heartRate, it.rawData, it.timestamp - startTimeStamp)
            }
        } else {
            heartRateEvents
        }
    } else {
        val hrEventsWithoutPauses = mutableListOf<WorkoutHrEvent>()

        val hrEventIterator = heartRateEvents.listIterator()
        val multisportAdjustedEvents = buildList {
            if (!startEvents.contains(events.first().type) && startTimeStamp != null) {
                // Multisports might not have start / continue where the sport type changes,
                // if that's the case add one at startTimeStamp's time to make sure we won't
                // skip events until first real start event
                val firstRealEvent = events.first()

                @Suppress("DEPRECATION") // multisport parts aren't affected by the iOS recording bug
                val fakeEventMillisInWorkout = firstRealEvent.timeWhenEventHappened - (firstRealEvent.realTime - startTimeStamp)
                val fakeStart = Event(
                    startEvents.first(),
                    startTimeStamp,
                    fakeEventMillisInWorkout
                )
                add(fakeStart)
            }

            addAll(events)

            if (!stopEvents.contains(events.last().type)) {
                // With multisports there might not be a pause / stop at the end of the part,
                // which confuses our logic below and makes it stop too early. Fix by adding
                // a fake stop event at last HR event
                val fakeStopAtEnd = Event(
                    stopEvents.first(),
                    heartRateEvents.last().timestamp,
                    heartRateEvents.last().millisecondsInWorkout.toDouble()
                )
                add(fakeStopAtEnd)
            }
        }

        val eventIterator = multisportAdjustedEvents.iterator()
        val realTimeOnStart = multisportAdjustedEvents.first().realTime
        var timeSpentPaused = 0L
        var pauseStartedAt: Long? = null
        var previousEvent: Event? = null

        val resumeEvents = setOf(Event.EventType.START, Event.EventType.CONTINUE)
        val pauseEvents = setOf(Event.EventType.PAUSE, Event.EventType.AUTOPAUSE)

        for (event in eventIterator) {
            if (resumeEvents.contains(event.type) && pauseStartedAt != null) {
                timeSpentPaused += event.realTime - pauseStartedAt
                pauseStartedAt = null
            } else if (pauseEvents.contains(event.type) && resumeEvents.contains(previousEvent?.type)) {
                pauseStartedAt = event.realTime
            }

            val workoutTimeExcludingPauses = previousEvent?.realTime?.let {
                (it - realTimeOnStart - timeSpentPaused).coerceAtLeast(0L)
            } ?: 0L

            while (hrEventIterator.hasNext()) {
                val hrEvent = hrEventIterator.next()

                if (hrEvent.timestamp <= event.realTime) {
                    @Suppress("ControlFlowWithEmptyBody")
                    if (previousEvent == null) {
                        // drop data before first start/resume
                        continue
                    } else if (resumeEvents.contains(previousEvent.type)) {
                        // Workout is being recorded. Calculate delta from the previous
                        // start/resume event and add current timestamp without pauses to it.
                        val delta = if (startTimeStamp == null || previousEvent.realTime > startTimeStamp) {
                            hrEvent.timestamp - previousEvent.realTime
                        } else {
                            hrEvent.timestamp - startTimeStamp
                        }
                        hrEventsWithoutPauses.add(
                            WorkoutHrEvent(
                                hrEvent.timestamp,
                                hrEvent.heartRate,
                                hrEvent.rawData,
                                workoutTimeExcludingPauses + delta
                            )
                        )
                    }
                } else {
                    // Rewind so that we won't lose the data and carry on from the next status event
                    hrEventIterator.previous()
                    break
                }
            }

            previousEvent = event
        }

        hrEventsWithoutPauses
    }
}
