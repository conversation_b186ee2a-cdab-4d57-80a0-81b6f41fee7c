package com.stt.android.domain.summaries;

import android.content.res.Resources;
import com.stt.android.domain.Filterable;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.ActivitySummaryExtensionsKt;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.infomodel.InfoModelUtilsKt;
import com.stt.android.utils.CalendarProvider;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeSet;

/**
 * Aggregation of workout header information grouping workouts by time frames (monthly,
 * weekly...) and highlighting a specific property from the workout (total time, distance,
 * energy...)
 */
public class WorkoutSummary implements Filterable {
    /**
     * Start time frame in milliseconds
     */
    private final long startTimestamp;
    /**
     * End time frame in milliseconds
     */
    private final long endTimestamp;
    private final ArrayList<WorkoutSummaryInfo> originalValues;
    /**
     * Convenient field to store the start year so we avoid calculating it again.
     */
    private final int startYear;
    private final int startMonth;
    private final int startDay;
    private final int endMonth;
    private final int endDay;
    private TreeSet<ActivityTyeWithFrequency> activityTypesSorted;
    /**
     * For convenience we use array list instead of list.
     */
    private ArrayList<WorkoutHeader> workouts;
    private double totalTimeInSeconds;
    private double totalDistanceInMeters;
    private double totalEnergy;
    private double totalAscent;
    private double averageHeartRate;
    /**
     * Keep track of how many seconds we have with HR information. So we can skip workouts which
     * don't have HR values.
     */
    private double totalHrTimeInSeconds;
    private double averageSpeed;
    /**
     * Keep track of how many seconds we have with average speed information. So we can skip
     * workouts which don't have
     * average speed values (e.g. indoors).
     */
    private double totalAvgSpeedTimeInSeconds;

    public WorkoutSummary(long startTimestamp, long endTimestamp,
        ArrayList<WorkoutSummaryInfo> workouts, CalendarProvider calendarProvider) {
        this.startTimestamp = startTimestamp;
        Calendar tmpCal = calendarProvider.getCalendar();
        tmpCal.setTimeInMillis(startTimestamp);
        this.startYear = tmpCal.get(Calendar.YEAR);
        this.startMonth = tmpCal.get(Calendar.MONTH);
        this.startDay = tmpCal.get(Calendar.DAY_OF_MONTH);

        this.endTimestamp = endTimestamp;
        tmpCal.setTimeInMillis(endTimestamp);
        this.endMonth = tmpCal.get(Calendar.MONTH);
        this.endDay = tmpCal.get(Calendar.DAY_OF_MONTH) - 1;

        this.originalValues = workouts;
        prepareDerivedValues(workouts);
    }

    /**
     * Calculates all the derived values based on the list of workouts given in temporary variables.
     * Then it calls
     * {@link #setValues(java.util.ArrayList, double, double, double, double, double, double, double,
     * double, java.util.TreeSet)}
     * to update the object fields so we avoid concurrency problems.
     * There might be integrity issues, for example the total energy might not be right for the
     * current workouts list
     * but eventually it is.
     */
    private void prepareDerivedValues(ArrayList<WorkoutSummaryInfo> workouts) {
        // We need to keep track of how many times an activity type appears in workouts
        Map<ActivityType, Integer> activityTypeFrequencies =
            new HashMap<>(ActivityType.getTotalNumberOfActivityTypes());
        double totalAvgHeartRateByTotalTime = 0.0;
        double totalAvgSpeedByTotalTime = 0.0;
        double totalTimeInSeconds = 0.0;
        double totalDistanceInMeters = 0.0;
        double totalEnergy = 0.0;
        double totalHrTimeInSeconds = 0.0;
        double totalAvgSpeedTimeInSeconds = 0.0;
        double totalAscent = 0.0;

        ArrayList<WorkoutHeader> workoutHeaders = new ArrayList<>();
        for (WorkoutSummaryInfo workoutSummaryInfo : workouts) {
            WorkoutHeader workout = workoutSummaryInfo.getWorkout();
            totalTimeInSeconds += workout.getTotalTime();
            totalDistanceInMeters += workout.getTotalDistance();
            totalEnergy += workout.getEnergyConsumption();
            if (workout.getHeartRateAverage() != 0.0) {
                totalHrTimeInSeconds += workout.getTotalTime();
                totalAvgHeartRateByTotalTime += workout.getTotalTime() * workout.getHeartRateAverage();
            }
            if (workout.getAvgSpeed() != 0.0) {
                totalAvgSpeedTimeInSeconds += workout.getTotalTime();
                totalAvgSpeedByTotalTime += workout.getTotalTime() * workout.getAvgSpeed();
            }
            double workoutAscent = workout.getTotalAscent();
            boolean supportsAscent = ActivitySummaryExtensionsKt.supportsAscent(workout);
            if (supportsAscent &&
                !InfoModelUtilsKt.shouldNotCountAscentForActivity(
                    workout.getActivityType().getId())) {
                totalAscent += workoutAscent;
            }

            ActivityType activityType = workout.getActivityType();
            Integer currentValue = activityTypeFrequencies.get(activityType);
            if (currentValue == null) {
                activityTypeFrequencies.put(activityType, 1);
            } else {
                activityTypeFrequencies.put(activityType, currentValue + 1);
            }
            workoutHeaders.add(workout);
        }
        double averageHeartRate =
            totalHrTimeInSeconds != 0.0 ? totalAvgHeartRateByTotalTime / totalHrTimeInSeconds : 0.0;
        double averageSpeed = totalAvgSpeedTimeInSeconds != 0.0 ? totalAvgSpeedByTotalTime
            / totalAvgSpeedTimeInSeconds : 0.0;

        // Now let's sort the activity types by frequency
        TreeSet<ActivityTyeWithFrequency> activityTypesSorted = new TreeSet<>();
        for (Map.Entry<ActivityType, Integer> activityTypeFrequency : activityTypeFrequencies
            .entrySet()) {
            activityTypesSorted.add(new ActivityTyeWithFrequency(activityTypeFrequency.getKey(),
                activityTypeFrequency.getValue()));
        }
        setValues(workoutHeaders, totalTimeInSeconds, totalDistanceInMeters, totalEnergy,
            totalHrTimeInSeconds, totalAvgSpeedTimeInSeconds, averageHeartRate, averageSpeed, totalAscent,
            activityTypesSorted);
    }


    private void setValues(ArrayList<WorkoutHeader> workouts, double totalTimeInSeconds,
        double totalDistanceInMeters, double totalEnergy, double totalHrTimeInSeconds,
        double totalAvgSpeedTimeInSeconds, double averageHeartRate, double averageSpeed, double totalAscent,
        TreeSet<ActivityTyeWithFrequency> activityTypesSorted) {
        this.workouts = workouts;
        this.totalTimeInSeconds = totalTimeInSeconds;
        this.totalDistanceInMeters = totalDistanceInMeters;
        this.totalEnergy = totalEnergy;
        this.totalHrTimeInSeconds = totalHrTimeInSeconds;
        this.totalAvgSpeedTimeInSeconds = totalAvgSpeedTimeInSeconds;
        this.averageHeartRate = averageHeartRate;
        this.averageSpeed = averageSpeed;
        this.activityTypesSorted = activityTypesSorted;
        this.totalAscent = totalAscent;
    }

    @Override
    public boolean applyFilter(CharSequence[] constraints, Resources resources) {
        ArrayList<WorkoutSummaryInfo> filteredWorkoutInfos;
        if (constraints == null || constraints.length == 0) {
            filteredWorkoutInfos = new ArrayList<>(originalValues);
        } else {
            filteredWorkoutInfos = new ArrayList<>();
            for (WorkoutSummaryInfo workoutSummaryInfo : originalValues) {
                if (workoutSummaryInfo.applyFilter(constraints, resources)) {
                    filteredWorkoutInfos.add(workoutSummaryInfo);
                }
            }
        }
        prepareDerivedValues(filteredWorkoutInfos);
        // We want to show empty summaries so we return true always
        return true;
    }

    public double getTotalTimeInSeconds() {
        return totalTimeInSeconds;
    }

    public double getTotalDistanceInMeters() {
        return totalDistanceInMeters;
    }

    public double getTotalEnergy() {
        return totalEnergy;
    }

    public double getTotalAscent() {
        return totalAscent;
    }

    /**
     * @return average heart rate weighted by the workouts duration.
     */
    public double getAverageHeartRate() {
        return averageHeartRate;
    }

    public long getStartTimestamp() {
        return startTimestamp;
    }

    public int getStartDay() {
        return startDay;
    }

    /**
     * @return the month number for the start summary (0 is January)
     */
    public int getStartMonth() {
        return startMonth;
    }

    public long getEndTimestamp() {
        return endTimestamp;
    }

    public int getEndDay() {
        return endDay;
    }

    /**
     * @return the month number for the end summary (0 is January)
     */

    public int getEndMonth() {
        return endMonth;
    }

    public int getStartYear() {
        return startYear;
    }

    public ArrayList<WorkoutHeader> getWorkouts() {
        return workouts;
    }

    public TreeSet<ActivityTyeWithFrequency> getActivityTypesSorted() {
        return activityTypesSorted;
    }

    public int getTotalWorkouts() {
        return workouts.size();
    }

    public double getTotalHrTimeInSeconds() {
        return totalHrTimeInSeconds;
    }

    public double getAverageSpeed() {
        return averageSpeed;
    }

    public double getTotalAvgSpeedTimeInSeconds() {
        return totalAvgSpeedTimeInSeconds;
    }

    public static class ActivityTyeWithFrequency implements Comparable<ActivityTyeWithFrequency> {
        private final ActivityType activityType;
        private final int frequency;

        private ActivityTyeWithFrequency(ActivityType activityType, int frequency) {
            this.activityType = activityType;
            this.frequency = frequency;
        }

        public ActivityType getActivityType() {
            return activityType;
        }

        public int getFrequency() {
            return frequency;
        }

        @Override
        public int compareTo(ActivityTyeWithFrequency another) {
            // First try to order them by frequency
            int orderByFrequency = another.frequency - frequency;
            // If the frequency is the same then order by activity ID
            int orderById = activityType.getId() - another.activityType.getId();
            return orderByFrequency == 0 ? orderById : orderByFrequency;
        }
    }
}
