package com.stt.android.domain.user;

import com.google.gson.annotations.SerializedName;

public class BackendPublicUser {
    @SerializedName("key")
    private final String key;
    @SerializedName("username")
    private final String username;
    @SerializedName("profileImageUrl")
    private final String profileImageUrl;
    @SerializedName("imageKey")
    private final String profileImageKey;
    @SerializedName("realName")
    private final String realName;
    @SerializedName("city")
    private final String city;
    @SerializedName("country")
    private final String country;
    @SerializedName("website")
    private final String website;

    private BackendPublicUser(String key, String username, String city, String country,
        String website, String profileImageUrl, String profileImageKey, String realName) {
        this.key = key;
        this.username = username;
        this.city = city;
        this.country = country;
        this.website = website;
        this.profileImageUrl = profileImageUrl;
        this.profileImageKey = profileImageKey;
        this.realName = realName;
    }

    public User getUser() {
        return User.publicInfo(key, username, realName, profileImageUrl, profileImageKey, website,
            city, country, null);
    }
}
