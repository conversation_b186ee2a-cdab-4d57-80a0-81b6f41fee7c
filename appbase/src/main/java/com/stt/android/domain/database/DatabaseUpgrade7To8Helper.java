package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import java.sql.SQLException;

public class DatabaseUpgrade7To8<PERSON>elper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade7To8Helper(SQLiteDatabase db, ConnectionSource connectionSource,
                                     DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        // added polyline to WorkoutHeader
        db.execSQL("ALTER TABLE " + LegacyWorkoutHeader.TABLE_NAME + " ADD COLUMN " + LegacyWorkoutHeader.DbFields.POLYLINE + ";");
    }
}
