package com.stt.android.domain.workout;

public class WorkoutCadenceEvent {
    /**
     * Timestamp (See {@link System#currentTimeMillis()} for this event.
     */
    public final long timestamp;
    public final int crankRPM;
    /**
     * Delta time since previous event
     */
    public final int timeDelta;
    public final float speedInMetersPerSecond;
    public final float distanceInMeters;

    public WorkoutCadenceEvent(long timestamp, int crankRoundsPerMinute, int wheelTimeDelta,
        double wheelRoundsPerSecond, int wheelRevolution, int wheelCircumference) {
        this.timestamp = timestamp;
        crankRPM = crankRoundsPerMinute;
        timeDelta = wheelTimeDelta;
        speedInMetersPerSecond = (float) wheelRoundsPerSecond * wheelCircumference / 1000;
        distanceInMeters = (float) wheelRevolution * wheelCircumference / 1000;
    }
}
