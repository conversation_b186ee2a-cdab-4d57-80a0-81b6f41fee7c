package com.stt.android.domain.database;

import androidx.annotation.NonNull;
import com.j256.ormlite.field.FieldType;
import com.j256.ormlite.field.SqlType;
import com.j256.ormlite.field.types.StringType;
import com.j256.ormlite.support.DatabaseResults;
import com.squareup.moshi.JsonAdapter;
import com.squareup.moshi.Types;
import com.stt.android.moshi.MoshiProvider;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

/**
 * Persists an integer list to the database as a JSON string
 */
public class JsonIntegerListPersister extends StringType {
    private static JsonIntegerListPersister singleton = new JsonIntegerListPersister();
    private static JsonAdapter<List<Integer>> adapter =
            MoshiProvider.INSTANCE.getInstance().adapter(Types.newParameterizedType(List.class, Integer.class));

    public JsonIntegerListPersister() {
        super(SqlType.STRING, new Class<?>[]{List.class});
    }

    public static JsonIntegerListPersister getSingleton() {
        return singleton;
    }

    @Override
    public Object sqlArgToJava(FieldType fieldType, Object sqlArg, int columnPos) throws SQLException {
        return sqlArg;
    }

    @Override
    public Object resultToSqlArg(FieldType fieldType, DatabaseResults results, int columnPos) throws SQLException {
        try {
            return stringToIntList(results.getString(columnPos));
        }
        catch (IOException e) {
            throw new SQLException("Failed to deserialize json string", e);
        }
    }

    @Override
    public Object javaToSqlArg(FieldType fieldType, Object javaObject) {
        return intListToString((List<Integer>) javaObject);
    }

    @NonNull
    public static String intListToString(List<Integer> javaObject) {
        return adapter.toJson(javaObject);
    }

    public static List<Integer> stringToIntList(String json) throws IOException {
        return adapter.fromJson(json);
    }
}
