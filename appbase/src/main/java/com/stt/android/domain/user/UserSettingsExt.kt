package com.stt.android.domain.user

import com.stt.android.data.usersettings.UserSettingsDefaults

val UserSettings.autoCommuteTaggingEnabled: Boolean get() = tagAutomation
    .getOrDefault(
        key = DomainUserTagAutomationSettings.AUTO_TAG_COMMUTE,
        defaultValue = UserSettingsDefaults.DEFAULT_AUTO_TAG_COMMUTE,
    )

fun UserSettings.autoCommuteTaggingEnabled(enabled: Boolean): UserSettings =
    setTagAutomation(DomainUserTagAutomationSettings.AUTO_TAG_COMMUTE, enabled)
