package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import android.os.SystemClock;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.field.FieldType;
import com.j256.ormlite.field.SqlType;
import com.j256.ormlite.field.types.SerializableType;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.support.DatabaseResults;
import com.j256.ormlite.table.DatabaseTable;
import com.stt.android.domain.Point;
import java.nio.ByteBuffer;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.Callable;
import timber.log.Timber;

/**
 * Helper class used to upgrade from database version 2 to 3
 */
public class DatabaseUpgrade2To3Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade2To3Helper(SQLiteDatabase db, ConnectionSource connectionSource,
                                     DatabaseHelper databaseHelper) {
            super(db, connectionSource, databaseHelper);
    }

    /**
     * In database v3 {@link com.stt.android.domain.workouts.WorkoutHeader} start, center and stop positions have changed
     * from being stored as a {@link java.io.Serializable} object to a custom format (See
     * {@link com.stt.android.domain.database.deprecated.PointPersister}). To migrate we use a custom persister (
     * {@link com.stt.android.domain.database.DatabaseUpgrade2To3Helper.V2toV3PointPersister})
     * that reads serialized Point and writes it back using the new custom format.
     *
     * @throws SQLException
     */
    @Override
    public void upgrade() throws SQLException {
        final Dao<WorkoutHeaderPositions, Integer> workoutHeaderV2Dao = databaseHelper.getDao(WorkoutHeaderPositions
                .class);
        long start = SystemClock.elapsedRealtime();
        try {
            workoutHeaderV2Dao.callBatchTasks(new Callable<Void>() {
                @Override
                public Void call() throws Exception {
                    // First read all workout headers stored in the local database (stripped-down version)
                    List<WorkoutHeaderPositions> allWorkoutPositions = workoutHeaderV2Dao.queryForAll();
                    // Then write the same workout header. The custom persister will take care to store the right format
                    for (WorkoutHeaderPositions workoutPosition : allWorkoutPositions) {
                        workoutHeaderV2Dao.update(workoutPosition);
                    }
                    return null;
                }
            });
        } catch (Exception e) {
            throw new SQLException("Unable to upgrade workout header table: " + e.getMessage());
        }
        long end = SystemClock.elapsedRealtime();
        Timber.d("DatabaseUpgrade2To3Helper.upgrade took %dms", (end - start));
    }

    /**
     * Stripped-down version of {@link com.stt.android.domain.workouts.WorkoutHeader} that only reads the id and the
     * three relevant positions (start, stop and center)
     */
    @DatabaseTable(tableName = WorkoutHeaderPositions.TABLE_NAME)
    private static class WorkoutHeaderPositions {
        public static final String TABLE_NAME = "workoutheader";
        @DatabaseField(id = true, columnName = DbFields.ID)
        private int id;
        @DatabaseField(persisterClass = V2toV3PointPersister.class, columnName = DbFields.START_POSITION)
        private Point startPosition;
        @DatabaseField(persisterClass = V2toV3PointPersister.class, columnName = DbFields.STOP_POSITION)
        private Point stopPosition;
        @DatabaseField(persisterClass = V2toV3PointPersister.class, columnName = DbFields.CENTER_POSITION)
        private Point centerPosition;

        public abstract static class DbFields {
            public static final String ID = "id";
            public static final String START_POSITION = "startPosition";
            public static final String STOP_POSITION = "stopPosition";
            public static final String CENTER_POSITION = "centerPosition";
        }

    }

    /**
     * Custom persister that reads serialized {@link Point} objects and writes them back
     * as an array of bytes (couple of doubles)
     */
    private static class V2toV3PointPersister extends SerializableType {
        /**
         * A double takes 8 bytes
         */
        public static final int SIZE_OF_DOUBLE = 8;
        private static V2toV3PointPersister singleton = new V2toV3PointPersister();

        public V2toV3PointPersister() {
            super(SqlType.BYTE_ARRAY, new Class<?>[0]);
        }

        public static V2toV3PointPersister getSingleton() {
            return singleton;
        }

        @Override
        public Object resultToSqlArg(FieldType fieldType, DatabaseResults results, int columnPos) throws SQLException {
            // When reading from the DB we want to read as serializable. So just call the super implementation
            return super.resultToSqlArg(fieldType, results, columnPos);
        }

        @Override
        public Object javaToSqlArg(FieldType fieldType, Object javaObject) throws SQLException {
            // When writing we want to write using our own implementation (2 doubles)
            Point point = (Point) javaObject;
            double longitude = point.getLongitude();
            double latitude = point.getLatitude();

            byte[] bytes = new byte[2 * SIZE_OF_DOUBLE];
            ByteBuffer.wrap(bytes).putDouble(longitude);
            ByteBuffer.wrap(bytes).putDouble(latitude);
            return bytes;
        }
    }
}
