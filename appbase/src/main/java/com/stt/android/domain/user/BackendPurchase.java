package com.stt.android.domain.user;

import com.google.gson.annotations.SerializedName;
import com.stt.android.billing.Purchase;

public class BackendPurchase {
    @SerializedName(value = "productID")
    private final String productId;
    @SerializedName(value = "purchaseID")
    private final String id;
    /**
     * Timestamp when the purchase occurred in seconds
     */
    @SerializedName(value = "purchaseDate")
    private final long date;
    @SerializedName(value = "purchaseRawInfo")
    private final String rawInfo;

    public BackendPurchase(Purchase purchase) {
        this.productId = purchase.getSku();
        this.id = purchase.getOrderId();
        // The com.stt.android.billing.Purchase stores the time in milliseconds,
        // we want it in seconds
        this.date = purchase.getPurchaseTime() / 1000;
        // FIXME: is original JSON providing too much info?
        this.rawInfo = purchase.getOriginalJson();
    }
}
