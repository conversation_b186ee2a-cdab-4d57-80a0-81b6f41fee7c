package com.stt.android.domain.workout;

import android.content.res.Resources;
import androidx.annotation.StringRes;
import com.stt.android.R;

import static com.stt.android.domain.user.MeasurementUnitKt.KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND;
import static com.stt.android.domain.user.MeasurementUnitKt.MILES_PER_HOUR_TO_METERS_PER_SECOND;

public enum AutoPause {
    OFF(R.string.off, 0, 0, 0f, "Off"),
    ZERO_KM_H(R.string.zero_amount_unit, 0, com.stt.android.core.R.string.km_h, 0, "0KM"),
    LESS_2_KM_H(R.string.less_amount_unit, 2, com.stt.android.core.R.string.km_h,
        2 * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND, "2KM"),
    LESS_5_KM_H(R.string.less_amount_unit, 5, com.stt.android.core.R.string.km_h,
        5 * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND, "5KM"),
    ZERO_MI_H(R.string.zero_amount_unit, 0, com.stt.android.core.R.string.mph, 0, "0MI"),
    LESS_1_MI_H(R.string.less_amount_unit, 1, com.stt.android.core.R.string.mph,
        1 * MILES_PER_HOUR_TO_METERS_PER_SECOND, "1MI"),
    LESS_3_MI_H(R.string.less_amount_unit, 3, com.stt.android.core.R.string.mph,
        3 * MILES_PER_HOUR_TO_METERS_PER_SECOND, "3MI");

    public static final AutoPause DEFAULT = OFF;

    /*
     * Default autoPause values. Curious fact: If below static fields are
     * defined in MeasurementUnit then their value is null when the Measurement
     * unit values are initialized.
     */
    public static final AutoPause METRIC_AUTOPAUSE[] = new AutoPause[] {
        AutoPause.OFF, AutoPause.ZERO_KM_H, AutoPause.LESS_2_KM_H, AutoPause.LESS_5_KM_H
    };
    public static final AutoPause IMPERIAL_AUTOPAUSE[] = new AutoPause[] {
        AutoPause.OFF, AutoPause.ZERO_MI_H, AutoPause.LESS_1_MI_H, AutoPause.LESS_3_MI_H
    };

    private final int localizedStringId;

    private final int amount;

    private final double amountInMS;

    @StringRes
    private final int unitResId;

    private final String simpleName;

    AutoPause(int localizedStringId, Integer amount, int unitResId, double amountInMetersPerSecond,
        String simpleName) {
        this.localizedStringId = localizedStringId;
        this.amount = amount;
        this.amountInMS = amountInMetersPerSecond;
        this.unitResId = unitResId;
        this.simpleName = simpleName;
    }

    public String getSimpleName() {
        return simpleName;
    }

    /**
     * @return the localized name of this activity
     */
    public String getLocalizedName(Resources resources) {
        return unitResId != 0 ? String.format(resources.getString(localizedStringId), amount,
            resources.getString(unitResId))
            : String.format(resources.getString(localizedStringId), amount, null);
    }

    public double getAmountInMetersPerSecond() {
        return amountInMS;
    }
}
