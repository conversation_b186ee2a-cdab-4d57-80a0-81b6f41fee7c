package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.database.deprecated.OldRouteTable;
import java.sql.SQLException;

public class DatabaseUpgrade14To15<PERSON>elper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade14To15Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, OldRouteTable.TABLE_NAME, OldRouteTable.DbFields.ACTIVITY_IDS);
    }
}
