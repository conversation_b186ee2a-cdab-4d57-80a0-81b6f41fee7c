package com.stt.android.domain.database

import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteDatabase.CONFLICT_ABORT
import android.database.sqlite.SQLiteQueryBuilder
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.LegacyWorkoutHeader
import com.stt.android.domain.user.VideoInformation
import java.sql.SQLException

/**
 * This migration updates values of WorkoutHeader.DbFields.EXTENSIONS_FETCHED column to false
 * in order to refetch fitnessExtension only.
 * https://suunto.tpondemand.com/entity/93635-use-estimatedvo2max-instead-of-vo2max-in
 *
 * in addition this migration also update pictures and videos rows with their respective
 * workout ID based on their workout key
 */
internal class DatabaseUpgrade46To47Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    helper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, helper) {

    private fun getWorkoutIdsWithFitnessExtension(): List<Int> {
        val qb = SQLiteQueryBuilder()
        qb.tables = "fitnessextension"
        val projectIn = arrayOf("workoutId")
        val workoutIds = mutableListOf<Int>()
        qb.query(db, projectIn, null, null, null, null, null).use { cursor ->
            if (cursor.moveToFirst()) {
                do {
                    @Suppress("Range")
                    val workoutId =
                        cursor.getInt(cursor.getColumnIndex("workoutId"))
                    workoutIds.add(workoutId)
                } while (cursor.moveToNext())
            }
        }
        return workoutIds
    }

    private fun updatePicturesWorkoutIds() {
        db.execSQL(
            """
            UPDATE ${ImageInformation.TABLE_NAME}
            SET ${ImageInformation.DbFields.WORKOUT_ID} = (
                SELECT ${LegacyWorkoutHeader.DbFields.ID} 
                FROM ${LegacyWorkoutHeader.TABLE_NAME} 
                WHERE ${LegacyWorkoutHeader.TABLE_NAME}.${LegacyWorkoutHeader.DbFields.KEY} = ${ImageInformation.TABLE_NAME}.${ImageInformation.DbFields.WORKOUT_KEY}
            )
            """.trimIndent()
        )
    }

    private fun updateVideosWorkoutIds() {
        db.execSQL(
            """
            UPDATE ${VideoInformation.TABLE_NAME}
            SET ${VideoInformation.DbFields.WORKOUT_ID} = (
                SELECT ${LegacyWorkoutHeader.DbFields.ID} 
                FROM ${LegacyWorkoutHeader.TABLE_NAME} 
                WHERE ${LegacyWorkoutHeader.TABLE_NAME}.${LegacyWorkoutHeader.DbFields.KEY} = ${VideoInformation.TABLE_NAME}.${VideoInformation.DbFields.WORKOUT_KEY}
            )
            """.trimIndent()
        )
    }

    @Throws(SQLException::class)
    override fun upgrade() {
        getWorkoutIdsWithFitnessExtension().forEach { workoutId ->
            val contentValues = ContentValues()
            contentValues.put(LegacyWorkoutHeader.DbFields.EXTENSIONS_FETCHED, false)
            val whereClause = "id =$workoutId"
            db.updateWithOnConflict(
                LegacyWorkoutHeader.TABLE_NAME,
                contentValues,
                whereClause,
                null,
                CONFLICT_ABORT
            )
        }

        updatePicturesWorkoutIds()
        updateVideosWorkoutIds()
    }
}
