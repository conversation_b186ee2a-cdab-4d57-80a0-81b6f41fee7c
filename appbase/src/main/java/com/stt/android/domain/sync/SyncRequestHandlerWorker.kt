package com.stt.android.domain.sync

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.ServiceInfo
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ForegroundInfo
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.stt.android.R
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.home.SyncFeedData
import com.stt.android.notifications.CHANNEL_ID_FOREGROUND_SYNC
import com.stt.android.utils.STTConstants
import timber.log.Timber
import javax.inject.Inject

class SyncRequestHandlerWorker(
    context: Context,
    params: WorkerParameters,
    private val syncRequestHandler: SyncRequestHandler,
    private val sharedPrefs: SharedPreferences,
) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {
        val request = inputData.getInt(REQUEST_BITMASK, 0)
        syncRequestHandler.runRequestInQueue(request)
        if (request == SyncRequest.pullAll()) {
            // Injecting SyncFeedData would result in circular dependency.
            SyncFeedData.saveLastSyncTimestamp(sharedPrefs)
        }
        return Result.success()
    }

    override suspend fun getForegroundInfo(): ForegroundInfo {
        val runInForeground = inputData.getBoolean(RUN_IN_FOREGROUND, false)
        return if (runInForeground) {
            val notificationId = STTConstants.NotificationIds.FOREGROUND_FEED_SYNC
            val notification =
                NotificationCompat.Builder(applicationContext, CHANNEL_ID_FOREGROUND_SYNC)
                    .setSmallIcon(R.drawable.icon_notification)
                    .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
                    .setContentTitle(applicationContext.getString(R.string.notification_foreground_sync_content))
                    .build()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                ForegroundInfo(notificationId, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC)
            } else {
                ForegroundInfo(notificationId, notification)
            }
        } else {
            super.getForegroundInfo()
        }
    }

    class Factory @Inject constructor(
        private val syncRequestHandler: SyncRequestHandler,
        private val sharedPrefs: SharedPreferences,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return SyncRequestHandlerWorker(
                context = context,
                params = params,
                syncRequestHandler = syncRequestHandler,
                sharedPrefs = sharedPrefs,
            )
        }
    }

    companion object {
        private const val REQUEST_BITMASK =
            "com.stt.android.data.workout.syncrequest.requestbitmask"
        private const val RUN_IN_FOREGROUND =
            "com.stt.android.data.workout.syncrequest.runInForeground"
        private const val WORK_NAME = "SyncRequestHandlerWorker"

        fun enqueue(
            workManager: WorkManager,
            request: SyncRequestBitmask,
            runInForeground: Boolean = false,
        ) {
            Timber.d("Enqueuing $WORK_NAME")

            // TODO: Figure out a way to merge requests run through WorkManager
            // Basically that would mean creating a new merged request and
            // then replace the old pending work.
            // Google for some reason doesn't expose inputData of work in WorkInfo
            // Maybe the WorkManager's tag system could be re-purposed for this?

            val builder = OneTimeWorkRequestBuilder<SyncRequestHandlerWorker>()
                .setInputData(
                    workDataOf(
                        REQUEST_BITMASK to request,
                        RUN_IN_FOREGROUND to runInForeground,
                    )
                )
                .setConstraints(
                    Constraints.Builder()
                        .setRequiredNetworkType(NetworkType.CONNECTED)
                        .build()
                )
                .apply {
                    if (runInForeground) {
                        setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                    }
                }

            workManager.enqueueUniqueWork(
                WORK_NAME,
                ExistingWorkPolicy.APPEND_OR_REPLACE,
                builder.build(),
            )
        }
    }
}
