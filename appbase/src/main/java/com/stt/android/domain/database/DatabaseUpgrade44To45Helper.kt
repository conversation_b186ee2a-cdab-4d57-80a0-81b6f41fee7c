@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.db.getString
import com.stt.android.domain.user.LegacyUser
import com.stt.android.domain.user.LegacyWorkoutHeader
import com.stt.android.domain.user.workout.SuuntoLogbookEntry
import com.stt.android.utils.STTConstants
import timber.log.Timber
import java.sql.SQLException

/**
 * This migration removes entries from 'logbookentry' table that contain
 * workouts references which do not belong to the current user.
 * This is bugfix for https://suunto.tpondemand.com/entity/90655-exercise-was-lost-after-sync
 */
class DatabaseUpgrade44To45Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {

    @Throws(SQLException::class)
    override fun upgrade() {
        val username = db.query(
            LegacyUser.TABLE_NAME,
            arrayOf(LegacyUser.DbFields.ID, LegacyUser.DbFields.USERNAME),
            "${LegacyUser.DbFields.ID} = ${STTConstants.CURRENT_USER_DEFAULT_ID}",
            null,
            null,
            null,
            null
        ).use {
            if (it.moveToFirst()) {
                return@use it.getString(LegacyUser.DbFields.USERNAME)
            } else {
                return@use null
            }
        }
        if (username != null) {
            db.execSQL(
                """
                DELETE FROM ${SuuntoLogbookEntry.TABLE_NAME}
                WHERE ${SuuntoLogbookEntry.DbFields.WORKOUT_ID} in (
                    SELECT ${LegacyWorkoutHeader.DbFields.ID} FROM ${LegacyWorkoutHeader.TABLE_NAME}
                    WHERE ${LegacyWorkoutHeader.DbFields.USERNAME} != '$username'
                )"""
            )
        } else {
            Timber.w("Not running migration 44 to 45 because user is not in DB")
        }
    }
}
