package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.user.LegacyWorkoutHeader
import java.sql.SQLException

class DatabaseUpgrade39To40Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(
            db,
            LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.MAX_ALTITUDE
        )
        DatabaseHelper.addColumnIfNotExist(
            db,
            LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.MIN_ALTITUDE
        )
    }
}
