package com.stt.android.domain.user

import android.content.Context
import com.amersports.formatter.Success
import com.amersports.formatter.UnitType
import com.stt.android.R
import com.stt.android.extensions.isRunningSeriesForMcId
import com.stt.android.extensions.isSailingSeriesForMcId
import com.stt.android.extensions.isSwimmingSeriesForMcId
import com.stt.android.extensions.isVerticalSportsForMcId
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workouts.tts.WorkoutTextToSpeech
import com.suunto.algorithms.data.HeartRate
import com.suunto.connectivity.voicefeedback.IVoiceFeedback
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject

class VoiceFeedbackImpl @Inject constructor(
    @ApplicationContext val context: Context,
    var infoModelFormatter: InfoModelFormatter
) : IVoiceFeedback {
    private var workoutTextToSpeech: WorkoutTextToSpeech? = null
    private var lastLanguage: String? = null

    override fun start() {
        val language = context.getString(R.string.tts_language)
        // current language and last language is difference
        if ((lastLanguage != null && lastLanguage != language) || workoutTextToSpeech == null) {
            workoutTextToSpeech = WorkoutTextToSpeech(context, language)
        }
        lastLanguage = language
    }

    override fun stop() {
        workoutTextToSpeech?.shutdown()
        workoutTextToSpeech = null
    }

    override fun sayCurrentHeartRate(heartRate: HeartRate) {
        workoutTextToSpeech?.sayCurrentHeartRateWithCurrent(heartRate)
    }

    override fun sayHeartRateZone(heartRateZone: Int) {
        workoutTextToSpeech?.sayHeartRateZone(heartRateZone)
    }

    override fun sayLapNumber(number: Int) {
        workoutTextToSpeech?.sayLapNumber(number)
    }

    override fun sayLapPace(isSwimmingSeries: Boolean, lapSpeed: Double, unit: Int) {
        if (isSwimmingSeries) {
            workoutTextToSpeech?.sayLapSwimPace(
                lapSpeed,
                unit.convertMeasurementUnit()
            )
        } else {
            workoutTextToSpeech?.sayLapPace(
                lapSpeed,
                unit.convertMeasurementUnit()
            )
        }
    }

    override fun sayLapSpeed(lapSpeed: Double, unit: Int) {
        workoutTextToSpeech?.sayLapSpeed(
            lapSpeed,
            unit.convertMeasurementUnit()
        )
    }

    override fun sayMaxHeartRate(maxHeartRate: HeartRate) {
        workoutTextToSpeech?.sayMaxHeartRate(maxHeartRate)
    }

    override fun sayLapHeartRate(heartRate: HeartRate) {
        workoutTextToSpeech?.sayLapHeartRate(heartRate)
    }

    override fun saySplitDistance(isSwimmingSeries: Boolean, isSailing: Boolean, distance: Double, unit: Int) {
        if (isSwimmingSeries) {
            infoModelFormatter.formatValue(
                SummaryItem.SWIMDISTANCE,
                distance,
                unit.convertUnityType()
            ).value?.let {
                workoutTextToSpeech?.saySwimDistance(it, unit.convertMeasurementUnit().swimDistanceUnit)
            }
        } else {
            workoutTextToSpeech?.saySplitDistance(
                distance = distance,
                unit = unit.convertMeasurementUnit(),
                isSailing = isSailing
            )
        }
    }

    override fun sayLapTime(timeInSeconds: Double) {
        workoutTextToSpeech?.sayLapTime(timeInSeconds)
    }

    override fun sayPower(splitPower: Double) {
        val result = infoModelFormatter.formatValue("PowerFourdigits", splitPower, true)
        if (result is Success) {
            workoutTextToSpeech?.sayPower(result.value)
        }
    }

    override fun sayAscent(splitAscent: Double, unit: Int) {
        infoModelFormatter.formatValue(
            SummaryItem.ASCENTALTITUDE,
            splitAscent,
            unit.convertUnityType()
        ).value?.let {
            workoutTextToSpeech?.sayAscent(
                it,
                unit.convertMeasurementUnit().altitudeUnit
            )
        }
    }

    override fun sayDescent(splitDescent: Double, unit: Int) {
        infoModelFormatter.formatValue(
            SummaryItem.DESCENTALTITUDE,
            splitDescent,
            unit.convertUnityType()
        ).value?.let {
            workoutTextToSpeech?.sayDescent(
                it,
                unit.convertMeasurementUnit().altitudeUnit
            )
        }
    }

    private fun Int.convertMeasurementUnit() =
        if (this == MeasurementUnit.METRIC.key) MeasurementUnit.METRIC else MeasurementUnit.IMPERIAL

    private fun Int.convertUnityType() =
        if (this == MeasurementUnit.METRIC.key) UnitType.METRIC else UnitType.IMPERIAL

    override fun sayAvgCadence(splitCadence: Double) {
        val result = infoModelFormatter.formatValue("CadenceFourdigits", splitCadence, true)
        if (result is Success) {
            workoutTextToSpeech?.sayAverageCadence(result.value.toInt())
        }
    }

    override fun sayTotalTime(timeInSeconds: Double) {
        workoutTextToSpeech?.sayTotalTimeWithTitle(timeInSeconds)
    }

    override fun isRunningSeries(mcId: Int?): Boolean {
        return mcId?.isRunningSeriesForMcId ?: false
    }

    override fun isSwimmingSeries(mcId: Int?): Boolean {
        return mcId?.isSwimmingSeriesForMcId ?: false
    }

    override fun isSailingSeries(mcId: Int?): Boolean {
        return mcId?.isSailingSeriesForMcId ?: false
    }

    override fun isVerticalSports(mcId: Int?): Boolean {
        return mcId?.isVerticalSportsForMcId ?: false
    }

    override fun isTrailRunning(mcId: Int?): Boolean {
        return mcId == ActivityMapping.TRAILRUNNING.mcId
    }

    override fun isTreadmill(mcId: Int?): Boolean {
        return mcId == ActivityMapping.TREADMILL.mcId
    }

    override fun sayStarted() {
        workoutTextToSpeech?.sayStart()
    }
}
