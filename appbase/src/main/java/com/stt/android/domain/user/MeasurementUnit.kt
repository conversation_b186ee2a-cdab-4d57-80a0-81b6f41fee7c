package com.stt.android.domain.user

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.workout.AutoPause
import com.stt.android.domain.workouts.extensions.AltitudeSetting
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Locale
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

// Conversion constants
const val ONE_HOUR_IN_MINUTES = 60.0
const val METERS_TO_KILOMETERS = 1.0 / 1000.0
const val METERS_TO_CENTIMETER = 100
const val KILOMETERS_TO_METERS = 1000.0
const val DECIMETERS_TO_METERS = 0.1
const val METERS_PER_SECOND_TO_KILOMETERS_PER_HOUR = 3.6
const val KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND = 1000.0 / 3600.0
const val METERS_PER_SECOND_TO_MILES_PER_HOUR = 2.23693629
const val METERS_TO_FEET = 3.28084
const val FEET_TO_METERS = 1 / 3.28084
const val MILES_TO_KILOMETERS = 1.609344
const val MILES_TO_METERS = MILES_TO_KILOMETERS * 1000.0
const val KILOMETERS_TO_100_METERS = 10.0
const val MILES_TO_100_YARDS = 17.60
const val METERS_TO_MILES = 0.000621371
const val METERS_TO_YARDS = 1.0936133
const val MILES_PER_HOUR_TO_KILOMETERS_PER_HOUR = MILES_TO_KILOMETERS
const val MILES_PER_HOUR_TO_METERS_PER_SECOND =
    MILES_PER_HOUR_TO_KILOMETERS_PER_HOUR * KILOMETERS_PER_HOUR_TO_METERS_PER_SECOND
const val GRAMS_TO_KILOGRAMS = 0.001
const val GRAMS_TO_POUNDS = 0.00220462
const val KILOPASCALS_TO_BARS = 0.01
const val KILOPASCALS_TO_PSIS = 0.145037738
const val BARS_TO_PASCALS = 100000
const val PSIS_TO_PASCALS = 6894.76
const val PSI_TO_MBAR = 68.9476
const val MBAR_TO_PSI = 0.0145
const val CUBIC_METERS_TO_CUBIC_FEET = 35.315
const val CUBIC_FEET_TO_CUBIC_METERS = 0.02832
const val CUBIC_METERS_PER_S_TO_LITERS_PER_MIN = 60000.0
const val CUBIC_METERS_PER_S_TO_CUBIC_FEET_PER_MIN = 2118.88
const val DISTANCE_PER_SECOND_TO_DISTANCE_PER_MINUTE = 60.0
const val METERS_IN_ONE_NAUTICAL_MILE = 1852
const val METERS_PER_SECOND_TO_KNOTS = 1.94384
const val HZ_TO_RPM = 60f
const val CENTIMETER_TO_INCH = 0.393700787
const val CENTIMETER_TO_FEET = 0.0328083989
const val SECONDS_TO_MILLISECONDS = 1000

@JvmField
val DEFAULT_MEASUREMENT_UNIT = MeasurementUnit.METRIC
private const val METRIC_KEY = 0
private const val IMPERIAL_KEY = 1
private const val KELVIN_ABSOLUTE_ZERO = -273.15f

/**
 * As per http://en.wikipedia.org/wiki/Miles return imperial for the  countries that use miles
 * based on the ISO
 * country code given by the sim card (http://en.wikipedia.org/wiki/Mobile_country_code)
 */
fun getMeasurementUnitByCountryIso(simCountryIso: String): MeasurementUnit {
    return if (Locale.UK.country.equals(simCountryIso, ignoreCase = true) || Locale.US.country
            .equals(simCountryIso, ignoreCase = true)
    ) {
        MeasurementUnit.IMPERIAL
    } else {
        MeasurementUnit.METRIC
    }
}

fun valueOf(key: Int): MeasurementUnit {
    return when (key) {
        METRIC_KEY -> MeasurementUnit.METRIC
        IMPERIAL_KEY -> MeasurementUnit.IMPERIAL
        else -> throw IllegalArgumentException("Unknown key $key")
    }
}

enum class MeasurementUnit(
    val key: Int,
    @StringRes altitudeUnitResId: Int,
    @StringRes longAltitudeUnitResId: Int,
    private val altitudeFactor: Double,
    @StringRes shortDistanceUnitResId: Int,
    private val shortDistanceFactor: Double,
    @StringRes distanceUnitResId: Int,
    @StringRes distanceUnitFullResId: Int,
    @StringRes swimDistanceUnitResId: Int,
    @StringRes swimDistanceUnitFullResId: Int,
    private val distanceFactor: Double,
    private val swimDistanceFactor: Double,
    @StringRes speedUnitResId: Int,
    @StringRes paceUnitResId: Int,
    @StringRes swimPaceUnitResId: Int,
    private val paceToSwimPaceFactor: Double,
    private val metersPerSecondFactor: Double,
    @StringRes weightUnitResId: Int,
    private val weightFactor: Double,
    val autoPauseValues: Array<AutoPause>,
    @StringRes temperatureUnit: Int,
    @StringRes pressureUnitResId: Int,
    private val pressureFactor: Double,
    @StringRes gasConsumptionUnitResId: Int,
    private val gasConsumptionFactor: Double,
    @StringRes verticalSpeedUnitResId: Int,
    private val verticalSpeedFactor: Double,
    @StringRes shorterDistanceUnitResId: Int,
    private val shorterDistanceFactor: Double,
    @StringRes shortSpeedUnitResId: Int,
) {
    METRIC(
        key = METRIC_KEY,
        altitudeUnitResId = CR.string.meters,
        longAltitudeUnitResId = CR.string.meters_long,
        altitudeFactor = 1.0,
        shortDistanceUnitResId = CR.string.meters,
        shortDistanceFactor = 1.0,
        distanceUnitResId = CR.string.km,
        distanceUnitFullResId = CR.string.kilometers,
        distanceFactor = METERS_TO_KILOMETERS,
        swimDistanceFactor = 1.0,
        swimDistanceUnitResId = CR.string.meters,
        swimDistanceUnitFullResId = CR.string.meters_long,
        speedUnitResId = CR.string.km_h,
        paceUnitResId = CR.string.per_km,
        swimPaceUnitResId = CR.string.per_100_m,
        paceToSwimPaceFactor = 1 / KILOMETERS_TO_100_METERS,
        metersPerSecondFactor = METERS_PER_SECOND_TO_KILOMETERS_PER_HOUR,
        weightUnitResId = CR.string.kilograms,
        weightFactor = GRAMS_TO_KILOGRAMS,
        autoPauseValues = AutoPause.METRIC_AUTOPAUSE,
        temperatureUnit = R.string.celsius,
        pressureUnitResId = CR.string.bar,
        pressureFactor = KILOPASCALS_TO_BARS,
        gasConsumptionUnitResId = CR.string.liters_per_minute,
        gasConsumptionFactor = CUBIC_METERS_PER_S_TO_LITERS_PER_MIN,
        verticalSpeedUnitResId = CR.string.m_min,
        verticalSpeedFactor = DISTANCE_PER_SECOND_TO_DISTANCE_PER_MINUTE,
        shorterDistanceUnitResId = CR.string.cm,
        shorterDistanceFactor = 1.0,
        shortSpeedUnitResId = CR.string.m_s
    ),

    IMPERIAL(
        key = IMPERIAL_KEY,
        altitudeUnitResId = CR.string.feet,
        longAltitudeUnitResId = CR.string.feet_long,
        altitudeFactor = METERS_TO_FEET,
        shortDistanceUnitResId = CR.string.feet,
        shortDistanceFactor = METERS_TO_FEET,
        distanceUnitResId = CR.string.mile,
        distanceUnitFullResId = CR.string.miles,
        swimDistanceUnitResId = CR.string.TXT_YD,
        swimDistanceUnitFullResId = CR.string.TXT_YD,
        distanceFactor = METERS_TO_MILES,
        swimDistanceFactor = METERS_TO_YARDS,
        speedUnitResId = CR.string.mph,
        paceUnitResId = CR.string.per_mi,
        swimPaceUnitResId = CR.string.per_100_yard,
        paceToSwimPaceFactor = 1 / MILES_TO_100_YARDS,
        metersPerSecondFactor = METERS_PER_SECOND_TO_MILES_PER_HOUR,
        weightUnitResId = CR.string.pounds,
        weightFactor = GRAMS_TO_POUNDS,
        autoPauseValues = AutoPause.IMPERIAL_AUTOPAUSE,
        temperatureUnit = R.string.fahrenheit,
        pressureUnitResId = CR.string.psi,
        pressureFactor = KILOPASCALS_TO_PSIS,
        gasConsumptionUnitResId = CR.string.cubic_feet_per_minute,
        gasConsumptionFactor = CUBIC_METERS_PER_S_TO_CUBIC_FEET_PER_MIN,
        verticalSpeedUnitResId = CR.string.ft_min,
        verticalSpeedFactor = METERS_TO_FEET * DISTANCE_PER_SECOND_TO_DISTANCE_PER_MINUTE,
        shorterDistanceUnitResId = CR.string.inch,
        shorterDistanceFactor = CENTIMETER_TO_INCH,
        shortSpeedUnitResId = CR.string.ft_s
    );

    @StringRes
    val altitudeUnit: Int = altitudeUnitResId

    @StringRes
    val longAltitudeUnit: Int = longAltitudeUnitResId

    @StringRes
    val shortDistanceUnit: Int = shortDistanceUnitResId

    @StringRes
    val distanceUnit: Int = distanceUnitResId

    @StringRes
    val distanceUnitFull: Int = distanceUnitFullResId

    @StringRes
    val speedUnit: Int = speedUnitResId

    @StringRes
    val verticalSpeedUnit: Int = verticalSpeedUnitResId

    @StringRes
    val paceUnit: Int = paceUnitResId

    @StringRes
    val swimPaceUnit: Int = swimPaceUnitResId

    @StringRes
    val swimDistanceUnit: Int = swimDistanceUnitResId

    @StringRes
    val swimDistanceUnitFull: Int = swimDistanceUnitFullResId

    @StringRes
    val weightUnit: Int = weightUnitResId

    @StringRes
    val pressureUnit: Int = pressureUnitResId

    @StringRes
    val gasConsumptionUnit: Int = gasConsumptionUnitResId

    @StringRes
    val temperatureUnit: Int = temperatureUnit

    @StringRes
    val shorterDistanceUnitResId: Int = shorterDistanceUnitResId

    @StringRes
    val shortSpeedUnitResId: Int = shortSpeedUnitResId

    @StringRes
    fun getAltitudeSettingUnit(altitudeSetting: AltitudeSetting): Int =
        if (this == IMPERIAL) {
            CR.string.feet
        } else {
            when (altitudeSetting) {
                AltitudeSetting.HIGH -> distanceUnit
                else -> CR.string.meters
            }
        }

    fun fromMetersToNauticalMile(meters: Double): Double {
        return meters / METERS_IN_ONE_NAUTICAL_MILE
    }

    /**
     * @return the speed in knots
     */
    fun toKnots(metersPerSecond: Double): Double {
        return metersPerSecond * METERS_PER_SECOND_TO_KNOTS
    }

    /**
     * @return the speed in meters per second
     */
    fun fromKnots(knots: Double): Double {
        return knots / METERS_PER_SECOND_TO_KNOTS
    }

    /**
     * @return the altitude in the desired unit (meters or feet)
     */
    fun toAltitudeUnit(meters: Double): Double {
        return meters * altitudeFactor
    }

    /**
     * @return the distance in the desired unit (m or feet)
     */
    fun toShortDistanceUnit(meters: Double): Double {
        return meters * shortDistanceFactor
    }

    /**
     * @return the distance in the desired unit (m or yards)
     */
    fun toSwimDistanceUnit(meters: Double): Double {
        return meters * swimDistanceFactor
    }

    /**
     * @return the distance in the desired unit (km or miles)
     */
    fun toDistanceUnit(meters: Double): Double {
        return meters * distanceFactor
    }

    /**
     * @return the speed in the desired unit (e.g. km/h or mph)
     */
    fun toSpeedUnit(metersPerSecond: Double): Double {
        return metersPerSecond * metersPerSecondFactor
    }

    /**
     * @return the vertical speed in desired unit (e.g. m/min or ft/min)
     */
    fun toVerticalSpeedUnit(metersPerSecond: Double): Double {
        return metersPerSecond * verticalSpeedFactor
    }

    /**
     * @return the vertical speed in meters/second
     */
    fun fromVerticalSpeedUnit(verticalSpeed: Double): Double {
        return verticalSpeed / verticalSpeedFactor
    }

    /**
     * @return the pace in the desired unit (e.g. min/km or min/mi)
     */
    fun toPaceUnit(speed: Double): Double {
        return if (speed == 0.0) {
            0.0
        } else {
            ONE_HOUR_IN_MINUTES / toSpeedUnit(speed)
        }
    }

    /**
     * @return the pace in the meters in second
     */
    fun fromPaceUnit(pace: Double): Double {
        return if (pace == 0.0) {
            0.0
        } else {
            fromSpeedUnit(ONE_HOUR_IN_MINUTES / pace)
        }
    }

    /**
     * @return the swim pace in the desired unit (metric: /100m, imperial: /100yd)
     */
    fun toSwimPaceUnit(speed: Double): Double {
        return toPaceUnit(speed) * paceToSwimPaceFactor
    }

    /**
     * @return the swim pace in meters/second
     */
    fun fromSwimPaceUnit(pace: Double): Double {
        return fromPaceUnit(pace / paceToSwimPaceFactor)
    }

    /**
     * Converts from grams to whatever weight unit required.
     *
     * @param grams Weight in grams.
     * @return Weight in the weight unit of this [MeasurementUnit].
     */
    fun toWeightUnit(grams: Int): Double {
        return grams * weightFactor
    }

    /**
     * Converts from cubic metres per second to either liters per minute or cubic feet per minute.
     *
     * @param gasConsumption Avg ventilation in cubic meters per second.
     * @return Avg ventilation in the gas consumption unit of this [MeasurementUnit]
     */
    fun toGasConsumptionUnit(gasConsumption: Double): Double {
        return gasConsumption * gasConsumptionFactor
    }

    /**
     * @param gasConsumption Gas consumption in current unit.
     * @return Gas consumption in cubic meters per second.
     */
    fun fromGasConsumptionUnit(gasConsumption: Double): Double {
        return gasConsumption / gasConsumptionFactor
    }

    /**
     * Converts from weight unit to grams.
     *
     * @param weight Weight in the weight unit of this [MeasurementUnit].
     * @return Weight in grams.
     */
    fun fromWeightUnit(weight: Double): Int {
        return (weight / weightFactor).roundToInt()
    }

    /**
     * @return the distance in meters (useful to store altitude in data model)
     */
    fun fromAltitudeUnit(altitude: Double): Double {
        return altitude / altitudeFactor
    }

    /**
     * @param distance The distance in the distance unit.
     * @return the distance in meters (useful to store distance in data model)
     */
    fun fromShortDistanceUnit(distance: Double): Double {
        return distance / shortDistanceFactor
    }

    /**
     * @param distance The distance in the distance unit.
     * @return the distance in meters (useful to store distance in data model)
     */
    fun fromDistanceUnit(distance: Double): Double {
        return distance / distanceFactor
    }

    fun fromNauticalMileToMeters(nauticalMile: Double): Double {
        return nauticalMile * METERS_IN_ONE_NAUTICAL_MILE
    }

    /**
     * @return the meter/s (useful to store speed in data model)
     */
    fun fromSpeedUnit(speed: Double): Double {
        return BigDecimal(
            speed.toString()
        ).divide(BigDecimal(metersPerSecondFactor.toString()), 20, RoundingMode.HALF_UP).toDouble()
    }

    /**
     * @param kelvin The temperature in Kelvin unit.
     * @return the temperature in either Celsius or Fahrenheit depending on metric or imperial
     */
    fun toTemperatureUnit(kelvin: Double): Double {
        return if (key == METRIC_KEY) fromKelvinToCelsius(kelvin) else fromKelvinToFahrenheit(kelvin)
    }

    /**
     * @param temperature temperature in either Celsius or Fahrenheit depending on metric or imperial
     * @return kelvin The temperature in Kelvin unit.
     */
    fun fromTemperatureUnit(temperature: Double): Double {
        return if (key == METRIC_KEY) fromCelsiusToKelvin(temperature) else fromFahrenheitToKelvin(
            temperature
        )
    }

    private fun fromKelvinToFahrenheit(kelvin: Double): Double {
        return (kelvin + KELVIN_ABSOLUTE_ZERO) * 9.0 / 5.0 + 32.0
    }

    private fun fromKelvinToCelsius(kelvin: Double): Double {
        return kelvin + KELVIN_ABSOLUTE_ZERO
    }

    private fun fromFahrenheitToKelvin(fahrenheit: Double): Double {
        return fromCelsiusToKelvin((fahrenheit - 32.0) * 5.0 / 9.0)
    }

    private fun fromCelsiusToKelvin(celsius: Double): Double {
        return celsius - KELVIN_ABSOLUTE_ZERO
    }

    /**
     * Converts from kilo pascals to desired pressure unit.
     *
     * @param kiloPascals Pressure in kilopascals.
     * @return Pressure in the pressure unit of this {@see MeasurementUnit}.
     */
    fun toPressureUnit(kiloPascals: Double): Double {
        return kiloPascals * pressureFactor
    }

    /**
     * Converts from pascals to desired pressure unit.
     *
     * @param pascals Pressure in pascals.
     * @param roundingMode what mode to use when rounding
     * @return Pressure in the pressure unit of this {@see MeasurementUnit}.
     * TODO this method is kinda used in dive customization and I am not sure how to share
     */
    fun fromPascalsToPressureUnit(
        pascals: Double,
        round: Boolean = false,
        roundingMode: RoundingMode = RoundingMode.HALF_UP
    ): Double {
        val bar = toPressureUnit(pascals / 1000)
        return if (round) {
            bar.toBigDecimal().setScale(1, roundingMode).toDouble()
        } else {
            bar
        }
    }

    /**
     * Converts from pressure unit to pascals.
     *
     * @param pressure Pressure in the pressure unit of this [MeasurementUnit].
     * @return Pressure in kilopascals.
     */
    fun fromPressureUnit(pressure: Double): Double {
        return pressure / pressureFactor
    }

    /**
     * @param decimeters Value in decimeters
     * @return Same value in meters
     */
    fun fromDecimetersToMeters(decimeters: Double): Double {
        return decimeters * DECIMETERS_TO_METERS
    }

    /**
     * @param meters Value in meters
     * @return Same value in centimeters
     */
    fun fromMeterToCentimeter(meters: Double): Double {
        return meters * METERS_TO_CENTIMETER
    }

    /**
     * From centimeter to meter
     *
     * @param centimeters
     * @return Same value in meters
     */
    fun fromCentimeterToMeter(centimeters: Double): Double {
        return centimeters / METERS_TO_CENTIMETER
    }

    /**
     * From seconds to milliseconds
     *
     * @param seconds Value in seconds
     * @return Same value in milliseconds
     */
    fun fromSecondsToMilliseconds(seconds: Double): Double {
        return seconds * SECONDS_TO_MILLISECONDS
    }

    /**
     * From milliseconds to seconds
     *
     * @param milliseconds
     * @return Same value in seconds
     */
    fun fromMillisecondsToSeconds(milliseconds: Double): Double {
        return milliseconds / SECONDS_TO_MILLISECONDS
    }

    /**
     * @param hertz Value in hz
     * @return Same value in RPM
     */
    fun fromHzToRpm(hertz: Double): Double {
        return hertz * HZ_TO_RPM
    }

    /**
     * @param rpm Value in RPM/BPM
     * @return Value in hz
     */
    fun fromRpmToHz(rpm: Double): Double {
        return rpm / HZ_TO_RPM
    }

    fun toCoreMeasurementUnit(): com.stt.android.core.domain.MeasurementUnit = when (this) {
        METRIC -> com.stt.android.core.domain.MeasurementUnit.METRIC
        IMPERIAL -> com.stt.android.core.domain.MeasurementUnit.IMPERIAL
    }

    /**
     * @param centimeters value in centimeters
     * @return jumpHeight in the desired unit (cm or inch)
     */
    fun toJumpHeightUnit(centimeters: Double): Double {
        return centimeters * shorterDistanceFactor
    }

    /**
     * @param metersPerSecond value in m/s
     * @return takeoffVelocity in the desired unit (m/s or ft/s)
     */
    fun toJumpHeightTakeoffVelocityUnit(metersPerSecond: Double): Double {
        return metersPerSecond * shortDistanceFactor
    }
}
