package com.stt.android.domain.user;

import com.google.gson.annotations.SerializedName;
import com.stt.android.domain.workouts.WorkoutsAggregateData;

public class BackendWorkoutsAggregateData {
    @SerializedName("totalDistance")
    private final double metersTracked;
    @SerializedName("workoutcount")
    private final int workouts;
    @SerializedName("totalTime")
    private final double totalTime;

    public BackendWorkoutsAggregateData(double metersTracked, int workouts, double totalTime) {
        this.metersTracked = metersTracked;
        this.workouts = workouts;
        this.totalTime = totalTime;
    }

    public WorkoutsAggregateData getAggregateData() {
        // Backend returns totalTime in centi seconds whereas if we query workoutsAggregate data
        // from local database, the totalTime is in seconds. So let's convert the totalTime to
        // seconds here
        double totalTimeInSeconds = totalTime / 100;
        return new WorkoutsAggregateData(metersTracked, workouts, totalTimeInSeconds);
    }
}
