package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.UpdateBuilder;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.ImageInformation;
import java.sql.SQLException;

public class DatabaseUpgrade13To14<PERSON><PERSON><PERSON> extends DatabaseUpgradeHelper {
    public DatabaseUpgrade13To14Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        // i screwed up the upgrade from 12 to 13, marking all images as "locally changed"
        // now i need to safely revert the change
        // basically, all "synced" images that have no "file name" should not be "locally changed"
        Dao<ImageInformation, Integer> imageDao = databaseHelper.getDao(ImageInformation.class);
        UpdateBuilder<ImageInformation, Integer> ub = imageDao.updateBuilder();
        ub.updateColumnValue(ImageInformation.DbFields.LOCALLY_CHANGED, false)
            .where()
            .isNotNull(ImageInformation.DbFields.KEY)
            .and()
            .isNull(ImageInformation.DbFields.FILE_NAME);
        ub.update();
    }
}
