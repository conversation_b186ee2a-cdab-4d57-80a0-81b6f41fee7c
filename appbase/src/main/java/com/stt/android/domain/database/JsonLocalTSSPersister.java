package com.stt.android.domain.database;

import androidx.annotation.NonNull;
import com.j256.ormlite.field.FieldType;
import com.j256.ormlite.field.SqlType;
import com.j256.ormlite.field.types.StringType;
import com.j256.ormlite.support.DatabaseResults;
import com.squareup.moshi.JsonAdapter;
import com.stt.android.data.source.local.workout.tss.LocalTSS;
import com.stt.android.moshi.MoshiProvider;
import java.io.IOException;
import java.sql.SQLException;

/**
 * DO NOT change package name of this class.
 * Persists {@link LocalTSS} as JSON string to DB
 */
public class JsonLocalTSSPersister extends StringType {
    private static final JsonLocalTSSPersister singleton = new JsonLocalTSSPersister();
    private static final JsonAdapter<LocalTSS> adapter = MoshiProvider.INSTANCE.getInstance().adapter(LocalTSS.class);

    public JsonLocalTSSPersister() {
        super(SqlType.STRING, new Class<?>[]{LocalTSS.class});
    }

    public static JsonLocalTSSPersister getSingleton() {
        return singleton;
    }

    @Override
    public Object sqlArgToJava(FieldType fieldType, Object sqlArg, int columnPos) {
        return sqlArg;
    }

    @Override
    public Object resultToSqlArg(FieldType fieldType, DatabaseResults results, int columnPos) throws SQLException {
        try {
            return stringToLocalTSS(results.getString(columnPos));
        }
        catch (IOException e) {
            throw new SQLException("Unable to deserialize json", e);
        }
    }

    public static LocalTSS stringToLocalTSS(String json) throws IOException {
        if (json == null) return null;
        return adapter.fromJson(json);
    }

    @Override
    public Object javaToSqlArg(FieldType fieldType, Object javaObject) {
        return LocalTSSToString((LocalTSS) javaObject);
    }

    @NonNull
    public static String LocalTSSToString(LocalTSS LocalTSS) {
        return adapter.toJson(LocalTSS);
    }
}
