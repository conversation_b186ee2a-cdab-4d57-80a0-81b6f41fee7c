package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.workoutextension.FitnessExtension;
import java.sql.SQLException;

class DatabaseUpgrade29To30Helper extends DatabaseUpgradeHelper {

    public DatabaseUpgrade29To30Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper helper) {
        super(db, connectionSource, helper);
    }

    @Override
    public void upgrade() throws SQLException {
        TableUtils.createTableIfNotExists(connectionSource, FitnessExtension.class);
    }
}
