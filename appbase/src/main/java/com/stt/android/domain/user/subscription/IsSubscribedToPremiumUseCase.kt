package com.stt.android.domain.user.subscription

import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import com.stt.android.BuildConfig
import com.stt.android.controllers.SubscriptionItemControllerDataSource
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_PREMIUM_FOR_DEBUG
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_PREMIUM_FOR_DEBUG_DEFAULT
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * In Suunto all users are treated as if they have Premium subscription
 */
class IsSubscribedToPremiumUseCase @Inject constructor(
    private val subscriptionItemDataSource: SubscriptionItemControllerDataSource,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
) {
    // We want to make sure this is only available for Debug build for obvious reasons.
    private val isDebugBuildAndHasPremiumToggleEnabled: Boolean get() =
        BuildConfig.DEBUG && featureTogglePreferences.getBoolean(KEY_ENABLE_PREMIUM_FOR_DEBUG, KEY_ENABLE_PREMIUM_FOR_DEBUG_DEFAULT)

    /**
     * @return
     *   In Suunto, always true.
     *   In Sports Tracker, if the user has Premium subscription or not.
     */
    operator fun invoke(): Flow<Boolean> = if (FlavorUtils.isSuuntoApp || isDebugBuildAndHasPremiumToggleEnabled) {
        flowOf(true)
    } else {
        subscriptionItemDataSource
            .validSubscriptionFlow()
            .map { it != null }
    }

    fun hasPremium(): LiveData<Boolean> = invoke().asLiveData()
}
