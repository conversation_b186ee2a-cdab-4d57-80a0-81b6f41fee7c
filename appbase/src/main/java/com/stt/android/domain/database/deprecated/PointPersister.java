package com.stt.android.domain.database.deprecated;

import com.j256.ormlite.field.FieldType;
import com.j256.ormlite.field.SqlType;
import com.j256.ormlite.field.types.ByteArrayType;
import com.j256.ormlite.support.DatabaseResults;
import com.stt.android.domain.Point;
import static com.stt.android.domain.database.MigrationUtil.bytesToPoint;
import static com.stt.android.domain.database.MigrationUtil.pointToBytes;
import java.sql.SQLException;

/**
 * Persist {@link Point} as byte array
 *
 * @deprecated Use {@link com.stt.android.domain.database.JsonPointPersister} instead
 */
@Deprecated
public class PointPersister extends ByteArrayType {
    private static PointPersister singleton = new PointPersister();

    public PointPersister() {
        super(SqlType.BYTE_ARRAY, new Class<?>[]{Point.class});
    }

    public static PointPersister getSingleton() {
        return singleton;
    }

    @Override
    public Object sqlArgToJava(FieldType fieldType, Object sqlArg, int columnPos) {
        return sqlArg;
    }

    @Override
    public Object resultToSqlArg(FieldType fieldType, DatabaseResults results, int columnPos) throws SQLException {
        byte[] bytes = results.getBytes(columnPos);
        return bytesToPoint(bytes);
    }

    @Override
    public Object javaToSqlArg(FieldType fieldType, Object javaObject) {
        Point point = (Point) javaObject;
        return pointToBytes(point);
    }
}
