package com.stt.android.domain.user.workout;

import com.github.mikephil.charting.data.CombinedData;
import com.stt.android.domain.workouts.WorkoutHeader;

public class RecentWorkoutSummary {
    public static class Summary {
        public final WorkoutHeader referenceWorkout;
        public final long startTime;
        public final long endTime;
        public final int workouts;
        public final int workoutsChange;
        public final double distance;
        public final int distanceChange;
        public final double energy;
        public final int energyChange;
        public final long duration;
        public final int durationChange;

        public Summary(WorkoutHeader referenceWorkout, long startTime, long endTime, int workouts,
                       int workoutsChange, double distance, int distanceChange, double energy,
                       int energyChange, long duration, int durationChange) {
            this.referenceWorkout = referenceWorkout;
            this.startTime = startTime;
            this.endTime = endTime;
            this.workouts = workouts;
            this.workoutsChange = workoutsChange;
            this.distance = distance;
            this.distanceChange = distanceChange;
            this.energy = energy;
            this.energyChange = energyChange;
            this.duration = duration;
            this.durationChange = durationChange;
        }
    }

    public final Summary summary;
    public final CombinedData duration;
    public final CombinedData distance;
    public final CombinedData speed;
    public final CombinedData pace;
    public final CombinedData energy;
    public final CombinedData averageHeartRate;
    public final CombinedData averageCadence;
    public final int highLightIndex;

    public RecentWorkoutSummary(Summary summary, CombinedData duration, CombinedData distance,
                                CombinedData speed, CombinedData pace, CombinedData energy,
                                CombinedData averageHeartRate, CombinedData averageCadence,
                                int highLightIndex) {
        this.summary = summary;
        this.duration = duration;
        this.distance = distance;
        this.speed = speed;
        this.pace = pace;
        this.energy = energy;
        this.averageHeartRate = averageHeartRate;
        this.averageCadence = averageCadence;
        this.highLightIndex = highLightIndex;
    }
}
