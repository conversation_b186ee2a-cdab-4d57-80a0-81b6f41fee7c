package com.stt.android.domain.database

import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao
import com.stt.android.domain.user.LegacyWorkoutHeader
import io.reactivex.schedulers.Schedulers
import java.sql.SQLException

/**
 * No changes to actual DB structure, but the preferred source to read
 * workout ascent & descent changed from SummaryExtension to WorkoutHeader so
 * copy the values from extensions to headers.
 *
 * No need to send the changes to backend, when a new workout is fetched from backend
 * similar migration is done at that point.
 */
class DatabaseUpgrade52To53Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper,
    private val summaryExtensionDao: SummaryExtensionDao
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        summaryExtensionDao
            .fetchAll()
            .subscribeOn(Schedulers.io())
            .blockingGet(emptyList())
            .forEach { extension ->
                @Suppress("DEPRECATION")
                val updatedValues = ContentValues().apply {
                    extension.ascent?.let {
                        put(LegacyWorkoutHeader.DbFields.TOTAL_ASCENT, it)
                    }
                    extension.descent?.let {
                        put(LegacyWorkoutHeader.DbFields.TOTAL_DESCENT, it)
                    }
                }

                if (updatedValues.size() > 0) {
                    db.update(
                        LegacyWorkoutHeader.TABLE_NAME,
                        updatedValues,
                        "${LegacyWorkoutHeader.DbFields.ID} = ?",
                        arrayOf(extension.workoutId.toString())
                    )
                }
            }
    }
}
