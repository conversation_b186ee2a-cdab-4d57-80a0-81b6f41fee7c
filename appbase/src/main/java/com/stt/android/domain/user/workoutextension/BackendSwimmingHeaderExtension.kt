package com.stt.android.domain.user.workoutextension

import com.google.gson.annotations.SerializedName
import com.stt.android.domain.workouts.extensions.SwimmingExtension

class BackendSwimmingHeaderExtension(
    @SerializedName("avgSwolf") val avgSwolf: Int? = null,
    @SerializedName("avgStrokeRate") val avgStrokeRate: Float? = null
) : BackendWorkoutExtension(TYPE) {

    constructor(se: SwimmingExtension) : this(se.avgSwolf)

    override fun toWorkoutExtension(workoutId: Int) =
        if (avgSwolf != null && avgStrokeRate != null) {
            SwimmingExtension(
                workoutId,
                avgSwolf,
                avgStrokeRate
            )
        } else {
            throw UnsupportedExtensionException("Cannot convert backend extension: $this")
        }

    override fun toString(): String {
        return "BackendSwimmingHeaderExtension(avgSwolf=$avgSwolf, avgStrokeRate=$avgStrokeRate)"
    }

    companion object {
        const val TYPE = "SwimmingHeaderExtension"
    }
}
