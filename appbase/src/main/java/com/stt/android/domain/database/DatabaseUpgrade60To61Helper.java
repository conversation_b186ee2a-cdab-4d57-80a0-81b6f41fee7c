package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.VideoInformation;
import java.sql.SQLException;

class DatabaseUpgrade60To61<PERSON><PERSON><PERSON> extends DatabaseUpgradeHelper {
    DatabaseUpgrade60To61Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, ImageInformation.TABLE_NAME,
            ImageInformation.DbFields.REVIEW_STATE);
        DatabaseHelper.addColumnIfNotExist(db, VideoInformation.TABLE_NAME,
            VideoInformation.DbFields.REVIEW_STATE);
    }
}
