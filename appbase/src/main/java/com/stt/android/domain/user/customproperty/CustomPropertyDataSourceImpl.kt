package com.stt.android.domain.user.customproperty

import com.stt.android.data.source.local.usercustomproperty.LocalUserCustomProperty
import com.stt.android.data.source.local.usercustomproperty.UserCustomPropertyDao
import com.stt.android.domain.user.CustomPropertyDataSource
import com.stt.android.domain.user.DomainCustomProperty
import javax.inject.Inject

class CustomPropertyDataSourceImpl @Inject constructor(
    private val customPropertyDao: UserCustomPropertyDao
) : CustomPropertyDataSource {
    override suspend fun saveData(domainCustomProperty: DomainCustomProperty) {
        customPropertyDao.save(domainCustomProperty.toLocal())
    }

    override suspend fun updateData(domainCustomProperty: DomainCustomProperty) {
        customPropertyDao.update(domainCustomProperty.toLocal())
    }

    override suspend fun findDataByKey(key: String): DomainCustomProperty? =
        customPropertyDao.findByKey(key)?.toDomain()

    override suspend fun findNotSyncData(): List<DomainCustomProperty> {
        val findNotSync = customPropertyDao.findNotSync()
        return findNotSync.map {
            it.toDomain()
        }
    }
}

fun DomainCustomProperty.toLocal(): LocalUserCustomProperty = LocalUserCustomProperty(
    key,
    value,
    valueType,
    isSynced
)

fun LocalUserCustomProperty.toDomain(): DomainCustomProperty = DomainCustomProperty(
    key,
    value,
    valueType,
    isSynced
)
