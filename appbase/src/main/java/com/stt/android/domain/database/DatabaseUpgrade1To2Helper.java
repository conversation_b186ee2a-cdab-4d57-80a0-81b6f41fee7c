package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import java.sql.SQLException;

/**
 * Helper class used to upgrade from database version 1 to 2
 */
public class DatabaseUpgrade1To2Helper extends DatabaseUpgradeHelper {

    private enum ACTIVITY_TYPES {
        WALKING(0),
        RUNNING(1),
        CYCLING(2),
        CROSS_COUNTRY_SKIING(3),
        OTHER_1(4),
        OTHER_2(5),
        OTHER_3(6),
        OTHER_4(7),
        OTHER_5(8),
        OTHER_6(9),
        MOUNTAIN_BIKING(10),
        HIKING(11),
        ROLLER_SKATING(12),
        DOWNHILL_SKIING(13),
        PADDLING(14),
        ROWING(15),
        GOLF(16),
        INDOOR(17);
        private final int id;

        private ACTIVITY_TYPES(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }
    }

    public DatabaseUpgrade1To2Helper(SQLiteDatabase db, ConnectionSource connectionSource,
                                     DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        // MyFriendFeedEvent class was removed
        db.execSQL("DROP TABLE IF EXISTS 'myfriendfeedevent'");
        // MyWorkoutCommentFeedEvent class was removed
        db.execSQL("DROP TABLE IF EXISTS 'myworkoutcommentfeedevent'");

        // Create index WorkoutHeader.USERNAME_START_TIME_IDX
        db.execSQL("CREATE INDEX IF NOT EXISTS 'username_startTime' ON 'workoutheader' ( 'username', 'startTime' )");
        updateWorkoutActivityTypesToIds(db, connectionSource);
    }

    private void updateWorkoutActivityTypesToIds(SQLiteDatabase db, ConnectionSource connectionSource) {
        db.execSQL("ALTER TABLE 'workoutheader' ADD COLUMN " + LegacyWorkoutHeader.DbFields.ACTIVITY_ID + " INTEGER;");
        for (ACTIVITY_TYPES activityType : ACTIVITY_TYPES.values()) {
            updateWorkoutActivityTypeToId(db, activityType.name(), activityType.getId());
        }
    }

    /**
     * updates all workout headers which match the activity type to the given
     * activity id.
     * 
     * @param db
     * @param activityId
     * @param activityTypeName
     */
    private void updateWorkoutActivityTypeToId(SQLiteDatabase db, String activityTypeName, int activityId) {
        db.execSQL("UPDATE workoutheader SET " + LegacyWorkoutHeader.DbFields.ACTIVITY_ID + "=" + activityId
                + " WHERE activityType='" + activityTypeName + "';");
    }

}
