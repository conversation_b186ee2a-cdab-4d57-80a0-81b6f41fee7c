package com.stt.android.domain.user.subscription

import com.stt.android.domain.user.UserSubscription

/**
 * Wrapper for [UserSubscription] that can be used to help distinguish
 * when the subscription status is still loading (CurrentPremiumSubscriptionStatus itself is null)
 * and when there is no subscription (userSubscription in CurrentPremiumSubscriptionStatus is null)
 */
data class CurrentPremiumSubscriptionStatus(
    val userSubscription: UserSubscription?,
    val hasFreeTrialAvailable: Boolean
) {
    val isSubscribed: Boolean
        get() = userSubscription != null
}
