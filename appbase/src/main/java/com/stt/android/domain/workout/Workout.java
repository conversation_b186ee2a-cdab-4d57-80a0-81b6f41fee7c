package com.stt.android.domain.workout;

import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.domain.workouts.WorkoutHeader;
import java.util.ArrayList;
import java.util.List;

/**
 * Workout representation with headers and data.
 */
public class Workout {
    private final WorkoutHeader header;
    private final WorkoutData data;
    private final List<ImageInformation> pictures;
    private final List<WorkoutExtension> extensions;

    public Workout(WorkoutHeader header, WorkoutData data, List<ImageInformation> pictures,
        List<WorkoutExtension> extensions) {
        this.header = header;
        this.data = data;
        // We need to link the pictures to the current header id
        this.pictures = setPicturesWorkoutId(header.getId(), pictures);
        this.extensions = extensions;
    }

    private List<ImageInformation> setPicturesWorkoutId(int workoutId,
        List<ImageInformation> pictures) {
        List<ImageInformation> result = new ArrayList<>(pictures.size());
        for (ImageInformation workoutPictureMetadata : pictures) {
            result.add(workoutPictureMetadata.linkWithWorkout(workoutId));
        }
        return result;
    }

    public WorkoutData getData() {
        return data;
    }

    public WorkoutHeader getHeader() {
        return header;
    }

    public List<ImageInformation> getPictures() {
        return pictures;
    }

    public List<WorkoutExtension> getExtensions() {
        return extensions;
    }
}
