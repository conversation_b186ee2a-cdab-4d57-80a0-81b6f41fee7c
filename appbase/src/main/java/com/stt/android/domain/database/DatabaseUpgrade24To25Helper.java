package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.VideoInformation;
import java.sql.SQLException;

public class DatabaseUpgrade24To25<PERSON><PERSON>per extends DatabaseUpgradeHelper {
    public DatabaseUpgrade24To25Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        TableUtils.createTableIfNotExists(connectionSource, VideoInformation.class);
    }
}
