package com.stt.android.domain.user;

import com.google.gson.annotations.SerializedName;

public class BackendPingData {
    @SerializedName("uniqueInstallationId")
    private final long uniqueInstallationId;
    @SerializedName("installationTimestamp")
    private final long installationTimestamp;
    @SerializedName("appStarts")
    private final int appStarts;
    /**
     * Total distance in meters
     */
    @SerializedName("totalDistance")
    private final double totalDistance;
    /**
     * Total time in seconds
     */
    @SerializedName("totalTime")
    private final double totalTime;
    @SerializedName("totalWorkouts")
    private final int totalWorkouts;
    /**
     * Application version number
     */
    @SerializedName("version")
    private final int appVersion;
    /**
     * Mobile country code. Usually 3 integers
     */
    @SerializedName("mcc")
    private final String mcc;
    /**
     * Mobile network code. Usually 2-3 integers
     */
    @SerializedName("mnc")
    private final String mnc;
    /**
     * Latitude
     */
    @SerializedName("lat")
    private final double lat;
    /**
     * Longitude
     */
    @SerializedName("lon")
    private final double lon;
    @SerializedName("deviceId")
    private final String deviceId;
    @SerializedName("imei")
    private final String imei;
    @SerializedName("platformVersion")
    private final String platformVersion;
    @SerializedName("hasHR")
    private final boolean hasHR;
    @SerializedName("platform")
    private final String platform;
    /**
     * If the user has a subscription the type of subscription.
     */
    @SerializedName("subscription")
    private final String subscription;

    @SuppressWarnings("unused")
    public BackendPingData(long uniqueInstallationId, long installationTimestamp, int appStarts,
                           double totalDistance, double totalTime, int totalWorkouts,
                           int appVersion, String mcc, String mnc, double lat, double lon,
                           String deviceId, String imei, String platformVersion, boolean hasHR,
                           String platform, String subscription) {
        this.uniqueInstallationId = uniqueInstallationId;
        this.installationTimestamp = installationTimestamp;
        this.appStarts = appStarts;
        this.totalDistance = totalDistance;
        this.totalTime = totalTime;
        this.totalWorkouts = totalWorkouts;
        this.appVersion = appVersion;
        this.mcc = mcc;
        this.mnc = mnc;
        this.lat = lat;
        this.lon = lon;
        this.deviceId = deviceId;
        this.imei = imei;
        this.platformVersion = platformVersion;
        this.hasHR = hasHR;
        this.platform = platform;
        this.subscription = subscription;
    }
}