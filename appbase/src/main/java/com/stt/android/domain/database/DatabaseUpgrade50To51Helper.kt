@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.user.LegacyUser
import java.sql.SQLException

class DatabaseUpgrade50To51Helper(
    db: SQLiteDatabase?,
    connectionSource: ConnectionSource?,
    databaseHelper: DatabaseHelper?
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {

    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(db, LegacyUser.TABLE_NAME, LegacyUser.DbFields.FIELD_TESTER)
    }
}
