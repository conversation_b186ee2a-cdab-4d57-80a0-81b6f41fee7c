package com.stt.android.domain.workout;

import androidx.annotation.NonNull;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.laps.CompleteLap;
import com.stt.android.tracker.event.Event;
import com.stt.android.domain.advancedlaps.Statistics;
import java.util.List;

public class WorkoutData {
    private final List<WorkoutGeoPoint> routePoints;
    private final List<WorkoutHrEvent> heartRateEvents;
    private final List<CompleteLap> manualLaps;
    private final MeasurementUnit measurementUnit;
    private final List<Event> events;
    private final Statistics longitudeStatistics;
    private final Statistics latitudeStatistics;
    private final Statistics altitudeStatistics;
    private final Statistics speedStatistics;
    private final Statistics heartRateStatistics;
    private final Statistics cadenceStatistics;
    private final float altitudeOffset;
    private final int heartRateMaximum;
    private final String mobileCountryCode;
    private final String mobileNetworkCode;

    public WorkoutData(@NonNull List<WorkoutGeoPoint> routePoints,
        List<WorkoutHrEvent> heartRateEvents, List<CompleteLap> manualLaps,
        MeasurementUnit measurementUnit, List<Event> events, Statistics longitudeStatistics,
        Statistics latitudeStatistics, Statistics altitudeStatistics, Statistics speedStatistics,
        Statistics heartRateStatistics, Statistics cadenceStatistics, float altitudeOffset,
        int heartRateMaximum, String mobileNetworkCode, String mobileCountryCode) {
        this.routePoints = routePoints;
        this.heartRateEvents = heartRateEvents;
        this.manualLaps = manualLaps;
        this.measurementUnit = measurementUnit;
        this.events = events;
        this.longitudeStatistics = longitudeStatistics;
        this.latitudeStatistics = latitudeStatistics;
        this.altitudeStatistics = altitudeStatistics;
        this.speedStatistics = speedStatistics;
        this.heartRateStatistics = heartRateStatistics;
        this.cadenceStatistics = cadenceStatistics;
        this.altitudeOffset = altitudeOffset;
        this.heartRateMaximum = heartRateMaximum;
        this.mobileNetworkCode = mobileNetworkCode;
        this.mobileCountryCode = mobileCountryCode;
    }

    public List<WorkoutGeoPoint> getRoutePoints() {
        return routePoints;
    }

    public List<WorkoutHrEvent> getHeartRateEvents() {
        return heartRateEvents;
    }

    public List<CompleteLap> getManualLaps() {
        return manualLaps;
    }

    public List<Event> getEvents() {
        return events;
    }

    public Statistics getLongitudeStatistics() {
        return longitudeStatistics;
    }

    public Statistics getLatitudeStatistics() {
        return latitudeStatistics;
    }

    public Statistics getAltitudeStatistics() {
        return altitudeStatistics;
    }

    public Statistics getSpeedStatistics() {
        return speedStatistics;
    }

    public Statistics getHeartRateStatistics() {
        return heartRateStatistics;
    }

    public Statistics getCadenceStatistics() {
        return cadenceStatistics;
    }

    public MeasurementUnit getMeasurementUnit() {
        return measurementUnit;
    }

    public float getAltitudeOffset() {
        return altitudeOffset;
    }

    public int getHeartRateMaximum() {
        return heartRateMaximum;
    }

    public String getMobileCountryCode() {
        return mobileCountryCode;
    }

    public String getMobileNetworkCode() {
        return mobileNetworkCode;
    }
}
