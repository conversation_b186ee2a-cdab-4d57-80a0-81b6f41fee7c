package com.stt.android.domain.user;

import androidx.annotation.NonNull;
import com.google.gson.annotations.SerializedName;

public class BackendReactionSummary {
    @SerializedName("utfCode")
    private final String reaction;

    @SerializedName("count")
    private final int count;

    @SerializedName("userReacted")
    private final boolean userReacted;

    public BackendReactionSummary(String reaction, int count, boolean userReacted) {
        this.reaction = reaction;
        this.count = count;
        this.userReacted = userReacted;
    }

    @NonNull
    public ReactionSummary toReactionSummary(String workoutKey) {
        return ReactionSummary.remote(workoutKey, reaction, count, userReacted);
    }

    // Necessary function to be able to create a proper WorkoutHeader from BackendReactionSummary
    public int getCount() {
        return count;
    }
}
