package com.stt.android.domain.database

import android.content.ContentValues
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.follow.UserFollowStatus
import timber.log.Timber
import java.sql.SQLException

class DatabaseUpgrade54To55Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper,
) : DatabaseUpgradeHelper(
    db,
    connectionSource,
    databaseHelper
) {

    @Throws(SQLException::class)
    override fun upgrade() {
        db.apply {
            val tableName = UserFollowStatus.TABLE_NAME
            val oldTableName = "${UserFollowStatus.TABLE_NAME}_old"

            // OrmLite index uses _idx as suffix
            execSQL("DROP INDEX IF EXISTS ${tableName}_status_idx;")
            execSQL("DROP INDEX IF EXISTS ${tableName}_direction_idx;")
            execSQL("DROP INDEX IF EXISTS ${tableName}_currentUserFollowStatus_idx;")

            execSQL("ALTER TABLE $tableName RENAME TO $oldTableName;")

            execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$tableName` (
                    `id` TEXT NOT NULL,
                    `username` TEXT NOT NULL,
                    `status` TEXT NOT NULL,
                    `realName` TEXT,
                    `profileDescription` TEXT,
                    `profileImageUrl` TEXT,
                    `coverImageUrl` TEXT,
                    `direction` TEXT NOT NULL,
                    `currentUserFollowStatus` TEXT,
                    `locallyChanged` INTEGER,
                    PRIMARY KEY(`id`))
                """.trimIndent()
            )

            execSQL("CREATE INDEX ${tableName}_status_idx ON $tableName (status);")
            execSQL("CREATE INDEX ${tableName}_direction_idx ON $tableName (direction);")
            execSQL("CREATE INDEX ${tableName}_currentUserFollowStatus_idx ON $tableName (currentUserFollowStatus);")

            query(oldTableName, null, null, null, null, null, null).use { cursor ->
                if (cursor.moveToFirst()) {
                    Timber.v("Loaded userFollowStatus data from old database table")
                    do {
                        insert(tableName, null, createContentValues(cursor))
                    } while (cursor.moveToNext())
                }
            }
            execSQL("DROP TABLE $oldTableName;")

            Timber.d("Successfully migrated userFollowStatus to database")
        }
    }

    private fun createContentValues(cursor: Cursor) = ContentValues().apply {
        put(
            UserFollowStatus.DbFields.ID,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.ID))
        )
        put(
            UserFollowStatus.DbFields.USERNAME,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.USERNAME))
        )
        put(
            UserFollowStatus.DbFields.STATUS,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.STATUS))
        )
        put(
            UserFollowStatus.DbFields.REAL_NAME,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.REAL_NAME))
        )
        put(
            UserFollowStatus.DbFields.PROFILE_DESCRIPTION,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.PROFILE_DESCRIPTION))
        )
        put(
            UserFollowStatus.DbFields.PROFILE_IMAGE_URL,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.PROFILE_IMAGE_URL))
        )
        put(
            UserFollowStatus.DbFields.COVER_IMAGE_URL,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.COVER_IMAGE_URL))
        )
        put(
            UserFollowStatus.DbFields.DIRECTION,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.DIRECTION))
        )
        put(
            UserFollowStatus.DbFields.CURRENT_USER_FOLLOW_STATUS,
            cursor.getString(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.CURRENT_USER_FOLLOW_STATUS))
        )
        put(
            UserFollowStatus.DbFields.LOCALLY_CHANGED,
            cursor.getInt(cursor.getColumnIndexOrThrow(UserFollowStatus.DbFields.LOCALLY_CHANGED))
        )
    }
}
