package com.stt.android.domain.user;

import com.google.gson.annotations.SerializedName;

/**
 * Maps the JSON returned by the backend when asking for facebook token
 */
public class FacebookToken {
    @SerializedName("facebookUid")
    private final String uid;
    @SerializedName("facebookToken")
    private final String token;


    private FacebookToken(String uid, String token) {
        this.uid = uid;
        this.token = token;
    }

    public String getUid() {
        return uid;
    }

    public String getToken() {
        return token;
    }
}
