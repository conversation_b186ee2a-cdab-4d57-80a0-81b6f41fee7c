package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.j256.ormlite.table.TableUtils;
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary;
import java.sql.SQLException;

public class DatabaseUpgrade18To19<PERSON>elper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade18To19Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        TableUtils.createTableIfNotExists(connectionSource, SlopeSkiSummary.class);
    }
}
