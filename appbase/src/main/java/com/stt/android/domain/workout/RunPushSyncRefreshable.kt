package com.stt.android.domain.workout

import com.stt.android.domain.refreshable.Refreshable
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandler
import javax.inject.Inject

class RunPushSyncRefreshable @Inject constructor(
    private val syncRequestHandler: SyncRequestHandler
) : Refreshable {
    override suspend fun refresh() {
        syncRequestHandler.runRequestInQueue(SyncRequest.push())
    }
}
