package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import java.sql.SQLException;

public class DatabaseUpgrade27To28<PERSON><PERSON>per extends DatabaseUpgradeHelper {

    public DatabaseUpgrade27To28Helper(SQLiteDatabase db,
        ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.RECOVERY_TIME);
    }
}
