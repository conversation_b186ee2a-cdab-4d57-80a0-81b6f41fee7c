package com.stt.android.domain.user;

import androidx.annotation.Nullable;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;

import java.lang.reflect.Type;
import java.util.Locale;

import timber.log.Timber;

/**
 * Represents a subscription that a user can purchase.
 */
public class SubscriptionInfo {
    private final String id;
    private final SubscriptionType type;
    private final SubscriptionLength length;
    private final boolean autoRenew;
    private final String localizedPrice;
    private final long fetchedTimestamp;
    private final Long freeTrialPeriodSeconds;

    public SubscriptionInfo(String id, SubscriptionType type, SubscriptionLength length,
                            boolean autoRenew, String localizedPrice, Long freeTrialPeriodSeconds) {
        // We use current timestamp to store when this item was fetched
        this(id, type, length, autoRenew, localizedPrice, System.currentTimeMillis(),
                freeTrialPeriodSeconds);
    }

    public SubscriptionInfo(String id, SubscriptionType type, SubscriptionLength length,
                             boolean autoRenew, String localizedPrice, long fetchedTimestamp,
                             Long freeTrialPeriodSeconds) {
        this.id = id;
        this.type = type;
        this.length = length;
        this.autoRenew = autoRenew;
        this.localizedPrice = localizedPrice;
        this.fetchedTimestamp = fetchedTimestamp;
        this.freeTrialPeriodSeconds = freeTrialPeriodSeconds;
    }

    /**
     * @return subscription item id (SKU)
     */
    public String getId() {
        return id;
    }

    public SubscriptionType getType() {
        return type;
    }

    public SubscriptionLength getLength() {
        return length;
    }

    public boolean isAutoRenew() {
        return autoRenew;
    }

    public String getLocalizedPrice() {
        return localizedPrice;
    }

    public boolean getAutoRenew() {
        return autoRenew;
    }

    public long getFetchedTimestamp() {
        return fetchedTimestamp;
    }

    @Nullable
    public Long getFreeTrialPeriodSeconds() {
        return freeTrialPeriodSeconds;
    }

    public SubscriptionInfo updateLocalizedPrice(String localizedPrice) {
        return new SubscriptionInfo(id, type, length, autoRenew, localizedPrice,
                fetchedTimestamp, freeTrialPeriodSeconds);
    }

    @Override
    public String toString() {
        return String.format(Locale.US, "SubscriptionInfo: [%s, %s, %s, %s, %s, %d]", id,
                type.name(), length.name(), autoRenew, localizedPrice, fetchedTimestamp);
    }

    public enum SubscriptionLength {
        MONTHLY,
        YEARLY,
        UNKNOWN;

        public static SubscriptionLength forValue(String value) {
            try {
                return SubscriptionLength.valueOf(value.toUpperCase());
            } catch (IllegalArgumentException e) {
                Timber.w("Unknown subscription length: %s", value);
                return SubscriptionLength.UNKNOWN;
            }
        }
    }

    public enum SubscriptionType {
        // It's unfortunate but the ACTIVE name was changed in the last minute to PREMIUM.
        // Backend should only return ACTIVE
        ACTIVE,
        IN_GRACE_PERIOD, // User subscription after payment fails but subscription is still active
        ON_HOLD, // User subscription that was put on hold due to failed payment
        UNKNOWN;

        public static SubscriptionType forValue(String value) {
            try {
                return SubscriptionType.valueOf(value.toUpperCase());
            } catch (IllegalArgumentException e) {
                Timber.w("Unknown subscription type: %s", value);
                return SubscriptionType.UNKNOWN;
            }
        }
    }

    public static class Deserializer implements JsonDeserializer<SubscriptionInfo> {
        @Override
        public SubscriptionInfo deserialize(JsonElement json, Type typeOfT,
                                            JsonDeserializationContext context) throws
                JsonParseException {
            JsonObject jsonObject = json.getAsJsonObject();
            String id = context.deserialize(jsonObject.get("id"), String.class);
            String subscriptionTypeStr = context.deserialize(jsonObject
                    .get("type"), String.class);
            SubscriptionType subscriptionType = SubscriptionType.forValue(subscriptionTypeStr);
            String subscriptionLengthStr = context.deserialize
                    (jsonObject.get("length"), String.class);
            SubscriptionInfo.SubscriptionLength subscriptionLength = SubscriptionLength.forValue
                    (subscriptionLengthStr);
            boolean autoRenew = context.deserialize(jsonObject.get("autoRenew"), boolean.class);
            String localizedPrice = context.deserialize(jsonObject.get("localizedPrice"),
                    String.class);
            Long freeTrialPeriodSeconds = context.deserialize(jsonObject.get
                    ("freeTrialPeriodSeconds"), Long.class);
            return new SubscriptionInfo(id, subscriptionType, subscriptionLength, autoRenew,
                    localizedPrice, freeTrialPeriodSeconds);
        }
    }
}
