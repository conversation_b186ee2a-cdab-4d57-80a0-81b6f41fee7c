package com.stt.android.domain.database

import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.stt.android.data.source.local.goaldefinition.GoalDefinitionDao
import com.stt.android.data.source.local.goaldefinition.LocalGoalDefinition
import com.stt.android.db.getBlob
import com.stt.android.db.getInt
import com.stt.android.db.getLong
import com.stt.android.db.getString
import com.stt.android.db.getStringOrNull
import io.reactivex.Completable
import io.reactivex.schedulers.Schedulers

private const val GOAL_DEFINITION_TABLE = "goalDefinition"
private const val ID = "id"
private const val USER_NAME = "userName"
private const val NAME = "name"
private const val TYPE = "type"
private const val PERIOD = "period"
private const val TARGET = "target"
private const val CREATED = "created"
private const val ACTIVITY_IDS = "activityIds"
private const val START_TIME = "startTime"
private const val END_TIME = "endTime"

class DatabaseUpgrade41To42Helper(
    db: SQLiteDatabase,
    dao: GoalDefinitionDao
) : RoomMigration<LocalGoalDefinition, GoalDefinitionDao>(db, dao, GOAL_DEFINITION_TABLE) {
    override fun insertDataEntities(dao: GoalDefinitionDao, dataEntities: List<LocalGoalDefinition>) {
        Completable.fromAction {
            dao.insert(dataEntities)
        }.subscribeOn(Schedulers.io())
            .blockingAwait()
    }

    override fun mapToDataEntity(cursor: Cursor): LocalGoalDefinition {
        return LocalGoalDefinition(
            id = cursor.getLong(ID),
            userName = cursor.getString(USER_NAME),
            name = cursor.getStringOrNull(NAME),
            type = cursor.getInt(TYPE),
            period = cursor.getInt(PERIOD),
            target = cursor.getInt(TARGET),
            created = cursor.getLong(CREATED),
            activityIds = MigrationUtil.byteArrayToIntList(cursor.getBlob(ACTIVITY_IDS)),
            startTime = cursor.getLong(START_TIME),
            endTime = cursor.getLong(END_TIME)

        )
    }
}
