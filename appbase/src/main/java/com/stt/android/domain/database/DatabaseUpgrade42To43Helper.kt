package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.user.LegacyWorkoutHeader
import java.sql.SQLException

class DatabaseUpgrade42To43Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        DatabaseHelper.addColumnIfNotExist(
            db,
            LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.EXTENSIONS_FETCHED
        )
    }
}
