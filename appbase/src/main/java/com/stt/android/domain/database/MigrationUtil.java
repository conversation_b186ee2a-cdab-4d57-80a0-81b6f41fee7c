package com.stt.android.domain.database;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.stt.android.domain.Point;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

/**
 * The methods in this class are used to read data that was stored as byte array.
 * This class should only be used in DB migration! Complex nested objects should be stored as JSON instead.
 */
public class MigrationUtil {
    private static final int BUFFER_SIZE_WITH_ALTITUDE = 24;
    private static final int BUFFER_SIZE_WITHOUT_ALTITUDE = 16;

    @NonNull
    public static byte[] intListToByteArray(List<Integer> javaObject) {
        List<Integer> integers = javaObject;
        byte[] bytes = new byte[4 * (integers.size() + 1)];
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        byteBuffer.putInt(integers.size());
        for (Integer integer : integers) {
            byteBuffer.putInt(integer);
        }
        return bytes;
    }

    public static List<Integer> byteArrayToIntList(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        int count = byteBuffer.getInt();
        List<Integer> integers = new ArrayList<>(count);
        for (int i = 0; i < count; ++i) {
            integers.add(byteBuffer.getInt());
        }
        return integers;
    }

    @Nullable
    public static Point bytesToPoint(@Nullable byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        Point point;
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        double longitude = byteBuffer.getDouble();
        double latitude = byteBuffer.getDouble();
        if (byteBuffer.hasRemaining()) {
            double altitude = byteBuffer.getDouble();
            point = new Point(longitude, latitude, altitude);
        }
        else {
            point = new Point(longitude, latitude);
        }
        return point;
    }

    @NonNull
    public static byte[] pointToBytes(Point point) {
        final double longitude = point.getLongitude();
        final double latitude = point.getLatitude();
        final byte[] bytes;

        if (point.getAltitude() != null) {
            bytes = new byte[BUFFER_SIZE_WITH_ALTITUDE];
        } else {
            bytes = new byte[BUFFER_SIZE_WITHOUT_ALTITUDE];
        }

        final ByteBuffer buffer = ByteBuffer.wrap(bytes)
            .putDouble(longitude)
            .putDouble(latitude);

        if (bytes.length == BUFFER_SIZE_WITH_ALTITUDE) {
            buffer.putDouble(point.getAltitude());
        }
        return bytes;
    }
}
