@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.data.source.local.IntListJsonConverter
import com.stt.android.data.source.local.routes.LocalRoute
import com.stt.android.data.source.local.routes.PointJsonConverter
import com.stt.android.data.source.local.routes.RouteDao
import com.stt.android.data.source.local.routes.RouteSegmentJsonConverter
import com.stt.android.domain.database.deprecated.OldRouteTable
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.rx2.rxCompletable
import timber.log.Timber
import java.sql.SQLException

class DatabaseUpgrade37To38Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper,
    private val routeDao: RouteDao,
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        val oldRoutesTable = "routes"
        val hasAlreadyMigrated = routeDao.getRoutesCountIncludingDeleted() > 0
        if (!hasAlreadyMigrated) {
            Timber.d("Migrating routes from old db to room db")
            db.apply {
                // Query for all routes excluding deleted ones
                query(oldRoutesTable, null, "${OldRouteTable.DbFields.DELETED} = ?", arrayOf("0"), null, null, null).use { cursor ->
                    if (cursor.moveToFirst()) {
                        Timber.v("Loaded routes from old database table")
                        do {
                            val localRoute = LocalRoute(
                                id = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.ID)),
                                watchRouteId = cursor.getInt(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.WATCH_ROUTE_ID)),
                                ownerUserName = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.OWNER_USER_NAME)),
                                key = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.KEY)) ?: "",
                                name = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.NAME)),
                                visibility = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.VISIBILITY)),
                                activityIds = IntListJsonConverter().toIntList(cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.ACTIVITY_IDS))),
                                averageSpeed = cursor.getDouble(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.AVERAGE_SPEED)),
                                totalDistance = cursor.getDouble(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.TOTAL_DISTANCE)),
                                ascent = 0.0,
                                descent = 0.0,
                                startPoint = PointJsonConverter().toNonNullPoint(cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.START_POINT))),
                                centerPoint = PointJsonConverter().toNonNullPoint(cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.CENTER_POINT))),
                                stopPoint = PointJsonConverter().toNonNullPoint(cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.STOP_POINT))),
                                locallyChanged = true,
                                modifiedDate = System.currentTimeMillis(),
                                deleted = false,
                                createdDate = cursor.getLong(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.CREATED)),
                                watchSyncState = cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.WATCH_SYNC_STATE)),
                                watchSyncResponseCode = cursor.getInt(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.WATCH_SYNC_RESPONSE_CODE)),
                                watchEnabled = cursor.getInt(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.WATCH_ENABLED)) == 1,
                                segments = RouteSegmentJsonConverter().toRouteSegment(cursor.getString(cursor.getColumnIndexOrThrow(OldRouteTable.DbFields.SEGMENTS)))
                            )
                            Timber.v("Inserting route: %s", localRoute)
                            rxCompletable {
                                routeDao.upsert(localRoute)
                            }.subscribeOn(Schedulers.io())
                                .blockingAwait()
                        } while (cursor.moveToNext())
                        Timber.d("Successfully migrated routes to Room db")
                    }
                }
                Timber.d("Dropping old routes table and index")
                execSQL("DELETE FROM $oldRoutesTable;")
            }
        }
    }
}
