package com.stt.android.domain

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.stt.android.domain.session.DomainUserSession
import com.stt.android.domain.session.EmailVerificationState
import kotlinx.parcelize.Parcelize
import java.io.Serializable

@Parcelize
data class UserSession(
    @SerializedName("sessionkey") val sessionKey: String?,
    @SerializedName("watchUserKey") val watchKey: String?,
    @SerializedName("facebookConnected") val isConnectedToFacebook: Boolean,
    @SerializedName("emailVerified") val emailVerificationState: EmailVerificationState,
) : Serializable, Parcelable {
    // TODO Check if the return value can be an immutable map
    fun getAuthorizationHeaders(): MutableMap<String, String> =
        mutableMapOf<String, String>().apply {
            sessionKey?.let { put(AUTHORIZATION_HEADER, it) }
        }

    companion object {
        const val AUTHORIZATION_HEADER: String = "STTAuthorization"

        private const val serialVersionUID = 877349660248105118L

        fun fromDomainSession(domainUserSession: DomainUserSession): UserSession = UserSession(
            sessionKey = domainUserSession.sessionKey,
            watchKey = domainUserSession.watchKey,
            isConnectedToFacebook = domainUserSession.facebookConnected,
            emailVerificationState = domainUserSession.emailVerificationState,
        )
    }
}
