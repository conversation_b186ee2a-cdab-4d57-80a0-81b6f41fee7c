package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import java.sql.SQLException;

class DatabaseUpgrade61To62<PERSON><PERSON>per extends DatabaseUpgradeHelper {
    DatabaseUpgrade61To62Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        db.execSQL("DROP TABLE IF EXISTS friendfeedevent");
        db.execSQL("DROP TABLE IF EXISTS workoutcommentfeedevent");
        db.execSQL("DROP TABLE IF EXISTS workoutfeedevent");
    }
}
