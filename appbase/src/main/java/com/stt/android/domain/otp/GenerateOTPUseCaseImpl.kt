package com.stt.android.domain.otp

import com.stt.android.controllers.UserSettingsController
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.otp.OTPGenerator
import javax.inject.Inject

class GenerateOTPUseCaseImpl
@Inject constructor(
    private val otpGenerator: OTPGenerator,
    private val userSettingsController: UserSettingsController
) : GenerateOTPUseCase {

    /**
     * Generates a TOTP with either provided [overrideSalt] or with logic from user data
     */
    override fun generateTOTP(overrideSalt: String?): String {
        val email = userSettingsController.settings.email
        val phoneNumber = userSettingsController.settings.phoneNumber
        val salt = when {
            !overrideSalt.isNullOrBlank() -> overrideSalt
            !email.isNullOrBlank() -> email
            !phoneNumber.isNullOrBlank() -> phoneNumber
            else -> OTPGenerator.DEFAULT_SALT_EMAIL
        }
        return otpGenerator.generateTOTP(salt)
    }
}
