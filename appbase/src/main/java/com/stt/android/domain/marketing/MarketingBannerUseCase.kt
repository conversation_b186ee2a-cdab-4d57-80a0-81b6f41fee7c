package com.stt.android.domain.marketing

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.data.marketing.MarketingBannerLocalMapper
import com.stt.android.data.source.local.marketing.MarketingBannerSharedPrefStorage
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.marketing.MarketingBannerInfo.Location
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE_DEFAULT
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.selects.select
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.hours

class MarketingBannerUseCase @Inject constructor(
    @Remote private val remoteDataSource: MarketingBannerDataSource,
    @Local private val localDataSource: MarketingBannerDataSource,
    private val storage: MarketingBannerSharedPrefStorage,
    private val mapper: MarketingBannerLocalMapper,
    currentUserController: CurrentUserController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    @FeatureTogglePreferences private val featureTogglePrefs: SharedPreferences,
) {
    private val username = currentUserController.currentUser.username

    private val _banners = MutableStateFlow<List<MarketingBannerInfo>>(emptyList())
    val banners = _banners.asStateFlow()

    suspend fun loadByLocation(
        location: Location,
    ): Unit = withContext(coroutinesDispatchers.io) {
        val localTask = async {
            runSuspendCatching {
                loadFromLocal().filterVisibleBannersByLocation(location)
            }.onFailure {
                Timber.d(it, "Failed to load banners from local")
            }
        }
        val remoteTask = async {
            runSuspendCatching {
                loadFromRemoteAndSaveToLocal().filterVisibleBannersByLocation(location)
            }.onFailure {
                Timber.d(it, "Failed to load banners from remote")
            }
        }
        val (localBannersResult, remoteBannersResult) = select {
            localTask.onAwait { Pair(it, null) }
            remoteTask.onAwait { Pair(null, it) }
        }
        if (localBannersResult == null) {
            remoteBannersResult!!.fold({
                _banners.tryEmit(it)
            }, {
                localTask.await().getOrNull()?.let {
                    _banners.tryEmit(it)
                }
            })
        } else {
            localBannersResult.getOrNull()?.takeIf { it.isNotEmpty() }?.let {
                _banners.tryEmit(it)
            }
            remoteTask.await().getOrNull()?.let {
                _banners.tryEmit(it)
            }
        }
    }

    suspend fun hideBanner(bannerId: Long): Unit = withContext(coroutinesDispatchers.io) {
        _banners.update { it.filter { banner -> banner.bannerId != bannerId } }
        buildSet {
            addAll(getHiddenBannerIds())
            add(bannerId.toString())
        }.let {
            setHiddenBannerIds(it)
        }
    }

    private suspend fun loadFromLocal(): List<MarketingBannerInfo> {
        return localDataSource.getMarketingBanners()
    }

    private suspend fun loadFromRemoteAndSaveToLocal(): List<MarketingBannerInfo> {
        if (featureTogglePrefs.getBoolean(
                KEY_ENABLE_MARKETING_BANNER_API_RATE,
                KEY_ENABLE_MARKETING_BANNER_API_RATE_DEFAULT,
            )
        ) {
            val updateMillis = storage.getUpdateMillis(username)
            if (System.currentTimeMillis() - updateMillis < 3.hours.inWholeMilliseconds) {
                throw IllegalStateException("Marketing banners were updated less than 3 hours ago")
            }
        }

        val banners = remoteDataSource.getMarketingBanners()
        storage.setMarketingBanners(username, mapper.toDataEntityList()(banners))
        storage.setUpdateMillis(username, System.currentTimeMillis())
        // Clean up hidden banner ids that are not in the new banners
        val bannerIdSet = banners.map { it.bannerId.toString() }.toSet()
        setHiddenBannerIds(getHiddenBannerIds().filter { it in bannerIdSet }.toSet())
        return banners
    }

    private fun getHiddenBannerIds(): Set<String> {
        return storage.getHiddenBannerIds(username)
    }

    private fun setHiddenBannerIds(hiddenBannerIds: Set<String>) {
        storage.setHiddenBannerIds(username, hiddenBannerIds)
    }

    private fun List<MarketingBannerInfo>.filterVisibleBannersByLocation(
        location: Location,
    ): List<MarketingBannerInfo> {
        val hiddenBannerIds = getHiddenBannerIds()
        val timestamp = System.currentTimeMillis()
        return filter { it.location == location }
            .filter { it.bannerId.toString() !in hiddenBannerIds }
            .filter { timestamp in it.startTime..it.endTime }
            .sortedBy { it.displayOrder }
            .take(MAX_COUNT)
    }

    companion object {
        private const val MAX_COUNT = 6
    }
}
