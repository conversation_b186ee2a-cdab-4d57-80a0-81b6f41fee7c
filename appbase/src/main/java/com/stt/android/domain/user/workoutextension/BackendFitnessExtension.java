package com.stt.android.domain.user.workoutextension;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;

public class BackendFitnessExtension extends BackendWorkoutExtension {
    public static final String TYPE = "FitnessExtension";

    @SerializedName("maxHeartRate")
    @Nullable
    private Integer maxHeartRate;

    @SerializedName("estimatedVo2Max")
    @Nullable
    private Float vo2Max;

    @SerializedName("fitnessAge")
    @Nullable
    private Integer fitnessAge;

    /**
     * No args constructor to make Gson safe. See:https://stackoverflow
     * .com/questions/18645050/is-default-no-args-constructor-mandatory-for-gson
     */
    protected BackendFitnessExtension() {
        super(TYPE);
    }

    public BackendFitnessExtension(@NonNull FitnessExtension extension) {
        super(TYPE);
        this.maxHeartRate = extension.getMaxHeartRate();
        this.vo2Max = extension.getVo2Max();
        this.fitnessAge = extension.getFitnessAge();
    }

    @NonNull
    @Override
    public WorkoutExtension toWorkoutExtension(int workoutId) throws UnsupportedExtensionException {
        if (maxHeartRate != null) {
            return new FitnessExtension(workoutId, maxHeartRate, vo2Max == null ? 0f : vo2Max, fitnessAge);
        } else {
            throw new UnsupportedExtensionException("Cannot convert backend extension: " + toString());
        }
    }

    @NonNull
    @Override
    public String toString() {
        return "BackendFitnessExtension{" +
            "maxHeartRate=" + maxHeartRate +
            ", vo2Max=" + vo2Max +
            ", fitnessAge=" + fitnessAge +
            '}';
    }
}
