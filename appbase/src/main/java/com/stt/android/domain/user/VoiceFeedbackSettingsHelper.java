package com.stt.android.domain.user;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;
import androidx.annotation.NonNull;

import com.stt.android.laps.Laps;
import com.stt.android.utils.STTConstants;

import java.util.Map;

public class VoiceFeedbackSettingsHelper {
    public static final int MAX_REPEATING_DISTANCE = 10000;

    // this is used to represents voice feedback setting for all activities
    public static final int GENERIC_ACTIVITY_ID = -1;

    private static final String DEFAULT_LAP_TYPE = Laps.Type.ONE.name();
    private static final String DEFAULT_ENABLED = "false";
    private static final String DEFAULT_AUTO_PAUSE_ENABLED = "false";
    private static final String DEFAULT_LAP_TIME_ENABLED = "true";
    private static final String DEFAULT_LAP_SPEED_PACE_ENABLED = "true";

    // for distance and duration, the default value (0) means "disabled"
    private static final String DEFAULT_CONTENT_DISTANCE = "0";
    private static final String DEFAULT_CONTENT_DURATION = "0";

    private static final String CONTENT_ENABLED = "enabled";
    private static final String CONTENT_AUTO_PAUSE = "autoPause";
    private static final String CONTENT_LAP_TIME = "lapTime";
    private static final String CONTENT_LAP_SPEED_PACE = "lapSpeedPace";
    private static final String CONTENT_LAP_TYPE = "lapType";

    private static final String CONTENT_DISTANCE = "distance";
    private static final String CONTENT_DURATION = "duration";
    private static final String CONTENT_ENERGY = "energy";
    private static final String CONTENT_CURRENT_SPEED_PACE = "currentSpeedPace";
    private static final String CONTENT_AVERAGE_SPEED_PACE = "averageSpeedPace";
    private static final String CONTENT_CURRENT_HEART_RATE = "currentHeartRate";
    private static final String CONTENT_AVERAGE_HEART_RATE = "averageHeartRate";
    private static final String CONTENT_CURRENT_CADENCE = "currentCadence";
    private static final String CONTENT_AVERAGE_CADENCE = "averageCadence";
    private static final String CONTENT_GHOST = "ghost";

    private static final String CONTENT_SUFFIX_PER_LAP = "PerLap";
    private static final String CONTENT_SUFFIX_DISTANCE = "Distance";
    private static final String CONTENT_SUFFIX_DURATION = "Duration";

    private static String buildKey(int activityTypeId, String content) {
        return STTConstants.DefaultPreferences.KEY_VOICE_FEEDBACK_SETTING_PREFIX
            + activityTypeId + "_" + content;
    }

    @NonNull
    public static VoiceFeedbackSettings getVoiceFeedbackSettings(SharedPreferences preferences,
                                                                 int activityTypeId) {
        boolean enabled = Boolean.parseBoolean(readSetting(
                preferences, activityTypeId, CONTENT_ENABLED, DEFAULT_ENABLED));

        boolean autoPause = Boolean.parseBoolean(readSetting(
                preferences, activityTypeId, CONTENT_AUTO_PAUSE, DEFAULT_AUTO_PAUSE_ENABLED));

        boolean lapTime = Boolean.parseBoolean(readSetting(
                preferences, activityTypeId, CONTENT_LAP_TIME, DEFAULT_LAP_TIME_ENABLED));

        boolean lapSpeedPace = Boolean.parseBoolean(readSetting(
                preferences, activityTypeId, CONTENT_LAP_SPEED_PACE, DEFAULT_LAP_SPEED_PACE_ENABLED));

        VoiceFeedbackSettings.Frequency distance
                = readFrequency(preferences, activityTypeId, CONTENT_DISTANCE);
        VoiceFeedbackSettings.Frequency duration
                = readFrequency(preferences, activityTypeId, CONTENT_DURATION);
        VoiceFeedbackSettings.Frequency energy
                = readFrequency(preferences, activityTypeId, CONTENT_ENERGY);
        VoiceFeedbackSettings.Frequency currentSpeedPace
                = readFrequency(preferences, activityTypeId, CONTENT_CURRENT_SPEED_PACE);
        VoiceFeedbackSettings.Frequency averageSpeedPace
                = readFrequency(preferences, activityTypeId, CONTENT_AVERAGE_SPEED_PACE);
        VoiceFeedbackSettings.Frequency currentHeartRate
                = readFrequency(preferences, activityTypeId, CONTENT_CURRENT_HEART_RATE);
        VoiceFeedbackSettings.Frequency averageHeartRate
                = readFrequency(preferences, activityTypeId, CONTENT_AVERAGE_HEART_RATE);
        VoiceFeedbackSettings.Frequency currentCadence
                = readFrequency(preferences, activityTypeId, CONTENT_CURRENT_CADENCE);
        VoiceFeedbackSettings.Frequency averageCadence
                = readFrequency(preferences, activityTypeId, CONTENT_AVERAGE_CADENCE);
        VoiceFeedbackSettings.Frequency ghost
                = readFrequency(preferences, activityTypeId, CONTENT_GHOST);
        Laps.Type lapType = Laps.Type.valueOf(
            readSetting(preferences, activityTypeId, CONTENT_LAP_TYPE, DEFAULT_LAP_TYPE));

        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPause, lapTime, lapSpeedPace,
            distance, duration, energy, currentSpeedPace, averageSpeedPace, currentHeartRate,
            averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    @NonNull
    public static VoiceFeedbackSettings getVoiceFeedbackSettings(Context context,
                                                                 int activityTypeId) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        return getVoiceFeedbackSettings(preferences, activityTypeId);
    }

    @NonNull
    private static String readSetting(SharedPreferences preferences, int activityTypeId, String content,
                                      String defaultValue) {
        String value = preferences.getString(buildKey(activityTypeId, content), null);
        if (value == null) {
            value = preferences.getString(buildKey(GENERIC_ACTIVITY_ID, content), defaultValue);
        }
        return value;
    }

    @NonNull
    private static VoiceFeedbackSettings.Frequency readFrequency(SharedPreferences preferences,
                                                                 int activityTypeId, String content) {
        // the default value of "perLap" depends on the content
        // with the following 4 enabled by default
        boolean defaultPerLapValue = CONTENT_GHOST.equals(content) || CONTENT_DISTANCE.equals(content)
                || CONTENT_DURATION.equals(content) || CONTENT_CURRENT_HEART_RATE.equals(content);
        boolean perLap = Boolean.parseBoolean(readSetting(
                preferences, activityTypeId, content + CONTENT_SUFFIX_PER_LAP,
                Boolean.toString(defaultPerLapValue)));

        int distance = Integer.parseInt(readSetting(
                preferences, activityTypeId, content + CONTENT_SUFFIX_DISTANCE, DEFAULT_CONTENT_DISTANCE));

        int duration = Integer.parseInt(readSetting(
                preferences, activityTypeId, content + CONTENT_SUFFIX_DURATION, DEFAULT_CONTENT_DURATION));

        return new VoiceFeedbackSettings.Frequency(perLap, distance, duration);
    }

    public static void saveVoiceFeedbackSettings(Context context,
                                                 VoiceFeedbackSettings voiceFeedbackSettings) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        saveVoiceFeedbackSettings(preferences, voiceFeedbackSettings);
    }

    public static void saveVoiceFeedbackSettings(SharedPreferences preferences,
                                                 VoiceFeedbackSettings voiceFeedbackSettings) {
        int activityTypeId = voiceFeedbackSettings.activityTypeId;
        SharedPreferences.Editor editor = preferences.edit();
        if (activityTypeId == GENERIC_ACTIVITY_ID) {
            for (Map.Entry<String, ?> entry : preferences.getAll().entrySet()) {
                String key = entry.getKey();
                if (key.startsWith("voice_feedback_")) {
                    editor.remove(key);
                }
            }
        }
        editor.putString(buildKey(activityTypeId, CONTENT_ENABLED),
            Boolean.toString(voiceFeedbackSettings.enabled))
            .putString(buildKey(activityTypeId, CONTENT_AUTO_PAUSE),
                Boolean.toString(voiceFeedbackSettings.autoPauseEnabled))
            .putString(buildKey(activityTypeId, CONTENT_LAP_TIME),
                Boolean.toString(voiceFeedbackSettings.lapTimeEnabled))
            .putString(buildKey(activityTypeId, CONTENT_LAP_SPEED_PACE),
                Boolean.toString(voiceFeedbackSettings.lapSpeedPaceEnabled))
            .putString(buildKey(activityTypeId, CONTENT_LAP_TYPE),
                voiceFeedbackSettings.lapType.name());
        saveFrequency(editor, activityTypeId, CONTENT_DISTANCE, voiceFeedbackSettings.distance);
        saveFrequency(editor, activityTypeId, CONTENT_DURATION, voiceFeedbackSettings.duration);
        saveFrequency(editor, activityTypeId, CONTENT_ENERGY, voiceFeedbackSettings.energy);
        saveFrequency(editor, activityTypeId, CONTENT_CURRENT_SPEED_PACE,
                voiceFeedbackSettings.currentSpeedPace);
        saveFrequency(editor, activityTypeId, CONTENT_AVERAGE_SPEED_PACE,
                voiceFeedbackSettings.averageSpeedPace);
        saveFrequency(editor, activityTypeId, CONTENT_CURRENT_HEART_RATE,
                voiceFeedbackSettings.currentHeartRate);
        saveFrequency(editor, activityTypeId, CONTENT_AVERAGE_HEART_RATE,
                voiceFeedbackSettings.averageHeartRate);
        saveFrequency(editor, activityTypeId, CONTENT_CURRENT_CADENCE,
                voiceFeedbackSettings.currentCadence);
        saveFrequency(editor, activityTypeId, CONTENT_AVERAGE_CADENCE,
                voiceFeedbackSettings.averageCadence);
        saveFrequency(editor, activityTypeId, CONTENT_GHOST, voiceFeedbackSettings.ghost);
        editor.apply();
    }

    private static void saveFrequency(SharedPreferences.Editor editor, int activityTypeId,
                                      String content, VoiceFeedbackSettings.Frequency value) {
        editor.putString(buildKey(activityTypeId, content + CONTENT_SUFFIX_PER_LAP),
                Boolean.toString(value.perLap))
                .putString(buildKey(activityTypeId, content + CONTENT_SUFFIX_DISTANCE),
                        Integer.toString(value.distance))
                .putString(buildKey(activityTypeId, content + CONTENT_SUFFIX_DURATION),
                        Integer.toString(value.duration));
    }

    public static void resetToDefault(Context context) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();
        for (Map.Entry<String, ?> entry : preferences.getAll().entrySet()) {
            String key = entry.getKey();
            if (key.startsWith("voice_feedback_")) {
                editor.remove(key);
            }
        }
        editor.apply();
    }

    public static void disableForAll(Context context) {
        disableForAll(PreferenceManager.getDefaultSharedPreferences(context));
    }

    public static void disableForAll(SharedPreferences preferences) {
        SharedPreferences.Editor editor = preferences.edit();
        for (Map.Entry<String, ?> entry : preferences.getAll().entrySet()) {
            String key = entry.getKey();
            if (key.startsWith("voice_feedback_") && key.contains(CONTENT_ENABLED)) {
                editor.remove(key);
            }
        }

        editor.apply();
    }
}
