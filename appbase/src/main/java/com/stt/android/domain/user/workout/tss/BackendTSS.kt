package com.stt.android.domain.user.workout.tss

import com.google.gson.annotations.SerializedName
import com.squareup.moshi.JsonClass

data class BackendTSS(
    @SerializedName("trainingStressScore") val trainingStressScore: Float,
    @SerializedName("calculationMethod") val calculationMethod: BackendTSSCalculationMethod,
    @SerializedName("intensityFactor") val intensityFactor: Float? = null,
    @SerializedName("normalizedPower") val normalizedPower: Float? = null,
    @SerializedName("averageGradeAdjustedPace") val averageGradeAdjustedPace: Float? = null
)

// using moshi annotation to prevent r8 to obscure this
@JsonClass(generateAdapter = false)
enum class BackendTSSCalculationMethod {
    @SerializedName("POWER")
    POWER,

    @SerializedName("PACE")
    PACE,

    @SerializedName("HR")
    HR,

    @SerializedName("SWIM_PACE")
    SWIM_PACE,

    @SerializedName("MET")
    MET,

    @SerializedName("MANUAL")
    MANUAL,

    @SerializedName("DYNAMIC_DFA")
    DYNAMIC_DFA,
}
