package com.stt.android.domain.user;

import com.stt.android.laps.Laps;

public class VoiceFeedbackSettings {
    public static class Frequency {
        public final boolean perLap;
        public final int distance;
        public final int duration;

        public Frequency(boolean perLap, int distance, int duration) {
            this.perLap = perLap;
            this.distance = distance;
            this.duration = duration;
        }
    }

    public final int activityTypeId;
    public final boolean enabled;
    public final boolean autoPauseEnabled;
    public final boolean lapTimeEnabled;
    public final boolean lapSpeedPaceEnabled;
    public final Frequency distance;
    public final Frequency duration;
    public final Frequency energy;
    public final Frequency currentSpeedPace;
    public final Frequency averageSpeedPace;
    public final Frequency currentHeartRate;
    public final Frequency averageHeartRate;
    public final Frequency currentCadence;
    public final Frequency averageCadence;
    public final Frequency ghost;
    public final Laps.Type lapType;

    public VoiceFeedbackSettings(int activityTypeId, boolean enabled, boolean autoPauseEnabled,
        boolean lapTimeEnabled, boolean lapSpeedPaceEnabled, Frequency distance, Frequency duration,
        Frequency energy, Frequency currentSpeedPace, Frequency averageSpeedPace,
        Frequency currentHeartRate, Frequency averageHeartRate, Frequency currentCadence,
        Frequency averageCadence, Frequency ghost, Laps.Type lapType) {
        super();
        this.activityTypeId = activityTypeId;
        this.enabled = enabled;
        this.autoPauseEnabled = autoPauseEnabled;
        this.lapTimeEnabled = lapTimeEnabled;
        this.lapSpeedPaceEnabled = lapSpeedPaceEnabled;
        this.distance = distance;
        this.duration = duration;
        this.energy = energy;
        this.currentSpeedPace = currentSpeedPace;
        this.averageSpeedPace = averageSpeedPace;
        this.currentHeartRate = currentHeartRate;
        this.averageHeartRate = averageHeartRate;
        this.currentCadence = currentCadence;
        this.averageCadence = averageCadence;
        this.ghost = ghost;
        this.lapType = lapType;
    }

    public VoiceFeedbackSettings updateActivityTypeId(int activityTypeId) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateEnabled(boolean enabled) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateAutoPauseEnabled(boolean autoPauseEnabled) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateLapTimeEnabled(boolean lapTimeEnabled) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateLapSpeedPaceEnabled(boolean lapSpeedPaceEnabled) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateDistance(Frequency distance) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateDuration(Frequency duration) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateEnergy(Frequency energy) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateCurrentSpeedPace(Frequency currentSpeedPace) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateAverageSpeedPace(Frequency averageSpeedPace) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateCurrentHeartRate(Frequency currentHeartRate) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateAverageHeartRate(Frequency averageHeartRate) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateCurrentCadence(Frequency currentCadence) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateAverageCadence(Frequency averageCadence) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateGhost(Frequency ghost) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }

    public VoiceFeedbackSettings updateLapType(Laps.Type lapType) {
        return new VoiceFeedbackSettings(activityTypeId, enabled, autoPauseEnabled, lapTimeEnabled,
            lapSpeedPaceEnabled, distance, duration, energy, currentSpeedPace, averageSpeedPace,
            currentHeartRate, averageHeartRate, currentCadence, averageCadence, ghost, lapType);
    }
}
