package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.stt.android.data.source.local.intensityextension.IntensityExtensionDao
import com.stt.android.data.source.local.intensityextension.LocalIntensityExtension
import com.stt.android.data.source.local.intensityextension.LocalWorkoutIntensityZone
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.db.containsTable
import com.stt.android.db.getBlobOrNull
import com.stt.android.db.getInt
import com.stt.android.domain.user.workout.WorkoutIntensityZone
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.io.ObjectInputStream

class DatabaseUpgrade58To59Helper(
    private val db: SQLiteDatabase,
    private val userDao: UserDao,
    private val intensityExtensionDao: IntensityExtensionDao
) {

    fun migrateToRoom() {
        // If user isn't logged in, no need to migrate
        val currentUsername = runBlocking(Dispatchers.IO) {
            userDao.findCurrentUser()?.username
        }

        if (currentUsername == null) {
            Timber.d("Skipping migrating intensity extension table contents, user logged out")
            removeOldTables()
            return
        }

        migrateIntensityExtensionItems()
        removeOldTables()
    }

    private fun migrateIntensityExtensionItems() {
        if (db.containsTable("intensityExtension")) {
            Timber.i("Migrating intensity extension from old db to room db")
            db.query(
                "intensityExtension",
                null,
                null,
                null,
                null,
                null,
                null
            ).use { itemCursor ->
                val items: List<LocalIntensityExtension> = buildList {
                    if (itemCursor.moveToFirst()) {
                        do {
                            val workoutId = itemCursor.getInt("workoutId")

                            val hrZones = try {
                                @Suppress("DEPRECATION")
                                itemCursor.getBlobOrNull("hrzones")?.inputStream()?.let {
                                    ObjectInputStream(it).readObject()
                                } as? WorkoutIntensityZone
                            } catch (e: Throwable) {
                                Timber.e(e, "Error parsing hrzones serializable for workoutId: $workoutId")
                                throw e
                            }

                            val speedZones = try {
                                @Suppress("DEPRECATION")
                                itemCursor.getBlobOrNull("speedzones")?.inputStream()?.let {
                                    ObjectInputStream(it).readObject()
                                } as? WorkoutIntensityZone
                            } catch (e: Throwable) {
                                Timber.e(e, "Error parsing speedzones serializable for workoutId: $workoutId")
                                throw e
                            }

                            val powerZones = try {
                                @Suppress("DEPRECATION")
                                itemCursor.getBlobOrNull("powerzones")?.inputStream()?.let {
                                    ObjectInputStream(it).readObject()
                                } as? WorkoutIntensityZone
                            } catch (e: Throwable) {
                                Timber.e(e, "Error parsing powerzones serializable for workoutId: $workoutId")
                                throw e
                            }

                            add(
                                LocalIntensityExtension(
                                    workoutId = workoutId,
                                    hrZones = hrZones?.toLocalWorkoutIntensityZone(),
                                    speedZones = speedZones?.toLocalWorkoutIntensityZone(),
                                    powerZones = powerZones?.toLocalWorkoutIntensityZone()
                                )
                            )
                        } while (itemCursor.moveToNext())
                    }
                }

                runBlocking(Dispatchers.IO) {
                    intensityExtensionDao.upsert(items)
                }
            }
        }
    }

    private fun removeOldTables() {
        Timber.i("Dropping old intensity extension table (table name intensityExtension")
        db.execSQL("DROP TABLE IF EXISTS intensityExtension")
    }

    private fun WorkoutIntensityZone.toLocalWorkoutIntensityZone(): LocalWorkoutIntensityZone {
        return LocalWorkoutIntensityZone(
            zone1Duration = zone1Duration,
            zone2Duration = zone2Duration,
            zone3Duration = zone3Duration,
            zone4Duration = zone4Duration,
            zone5Duration = zone5Duration,
            zone2LowerLimit = zone2LowerLimit,
            zone3LowerLimit = zone3LowerLimit,
            zone4LowerLimit = zone4LowerLimit,
            zone5LowerLimit = zone5LowerLimit
        )
    }
}
