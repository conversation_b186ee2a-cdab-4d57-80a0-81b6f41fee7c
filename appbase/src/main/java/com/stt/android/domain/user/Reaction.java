package com.stt.android.domain.user;

import android.provider.BaseColumns;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import timber.log.Timber;

public class Reaction {
    public static final String TABLE_NAME = "reaction";

    public abstract static class DbFields {
        public static final String ID = BaseColumns._ID;
        public static final String KEY = "key";
        public static final String WORKOUT_KEY = "workoutKey";
        public static final String REACTION = "reaction";
        public static final String USER_NAME = "userName";
        public static final String USER_REAL_NAME = "userRealName";
        public static final String USER_PROFILE_PICTURE_URL = "userProfilePictureUrl";
        public static final String TIMESTAMP = "timestamp";
        public static final String LOCALLY_CHANGED = "locallyChanged";
        public static final String DELETED = "deleted";
    }

    @DatabaseField(dataType = DataType.LONG, columnName = DbFields.ID, id = true)
    private final long id;

    @Nullable
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.KEY)
    private final String key;

    @NonNull
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.WORKOUT_KEY)
    private final String workoutKey;

    @ReactionSummary.ReactionType
    @NonNull
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.REACTION)
    private final String reaction;

    @NonNull
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.USER_NAME)
    private final String userName;

    @Nullable
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.USER_REAL_NAME)
    private final String userRealName;

    @Nullable
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.USER_PROFILE_PICTURE_URL)
    private final String userProfilePictureUrl;

    @DatabaseField(dataType = DataType.LONG, columnName = DbFields.TIMESTAMP)
    private final long timestamp;

    @DatabaseField(dataType = DataType.BOOLEAN, columnName = DbFields.LOCALLY_CHANGED)
    private final boolean locallyChanged;

    @DatabaseField(dataType = DataType.BOOLEAN, columnName = DbFields.DELETED)
    private final boolean deleted;

    /**
     * Only used by OrmLite.
     */
    Reaction() {
        //noinspection ConstantConditions
        this(0L, null, null, null, null, null, null, 0L, false, false);
    }

    private Reaction(long id, @Nullable String key, @NonNull String workoutKey,
        @NonNull String reaction, @NonNull String userName, @Nullable String userRealName,
        @Nullable String userProfilePictureUrl, long timestamp, boolean locallyChanged,
        boolean deleted) {
        this.id = id;
        this.key = key;
        this.workoutKey = workoutKey;
        this.reaction = reaction;
        this.userName = userName;
        this.userRealName = userRealName;
        this.userProfilePictureUrl = userProfilePictureUrl;
        this.timestamp = timestamp;
        this.locallyChanged = locallyChanged;
        this.deleted = deleted;
    }

    @Nullable
    public String getKey() {
        return key;
    }

    @NonNull
    public String getWorkoutKey() {
        return workoutKey;
    }

    @NonNull
    @ReactionSummary.ReactionType
    public String getReaction() {
        return reaction;
    }

    @NonNull
    public String getUserRealOrUserName() {
        return TextUtils.isEmpty(userRealName) ? userName : userRealName;
    }

    @NonNull
    public String getUserName() {
        return userName;
    }

    @Nullable
    public String getUserProfilePictureUrl() {
        return userProfilePictureUrl;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public boolean isDeleted() {
        return deleted;
    }

    @NonNull
    public Builder toBuilder() {
        return new Builder(id, key, workoutKey, reaction, userName, userRealName,
            userProfilePictureUrl, timestamp, locallyChanged, deleted);
    }

    public static Reaction local(@NonNull String workoutKey, @NonNull String reaction,
        @NonNull String userName, @Nullable String userRealName,
        @NonNull String userProfilePictureUrl, long timestamp, boolean deleted) {
        return new Builder().workoutKey(workoutKey)
            .reaction(reaction)
            .userName(userName)
            .userRealName(userRealName)
            .userProfilePictureUrl(userProfilePictureUrl)
            .timestamp(timestamp)
            .deleted(deleted)
            .locallyChanged(true)
            .build();
    }

    public static Reaction remote(@Nullable String key, @NonNull String workoutKey,
        @NonNull String reaction, @NonNull String userName, @Nullable String userRealName,
        @Nullable String userProfilePictureUrl, long timestamp) {
        return new Builder().key(key)
            .workoutKey(workoutKey)
            .reaction(reaction)
            .userName(userName)
            .userRealName(userRealName)
            .userProfilePictureUrl(userProfilePictureUrl)
            .timestamp(timestamp)
            .deleted(false)
            .synced()
            .build();
    }

    public static class Builder {
        private long id;
        @Nullable
        private String key;
        @NonNull
        private String workoutKey;
        @ReactionSummary.ReactionType
        @NonNull
        private String reaction;
        @NonNull
        private String userName;
        @Nullable
        private String userRealName;
        @Nullable
        private String userProfilePictureUrl;
        private long timestamp;
        private boolean locallyChanged;
        private boolean deleted;

        public Builder() {
            //noinspection ConstantConditions
            this(0L, null, null, null, null, null, null, 0L, false, false);
        }

        private Builder(long id, @Nullable String key, @NonNull String workoutKey,
            @NonNull String reaction, @NonNull String userName, @Nullable String userRealName,
            @Nullable String userProfilePictureUrl, long timestamp, boolean locallyChanged,
            boolean deleted) {
            this.id = id;
            this.key = key;
            this.workoutKey = workoutKey;
            this.reaction = reaction;
            this.userName = userName;
            this.userRealName = userRealName;
            this.userProfilePictureUrl = userProfilePictureUrl;
            this.timestamp = timestamp;
            this.locallyChanged = locallyChanged;
            this.deleted = deleted;
        }

        @NonNull
        public Builder key(String key) {
            this.key = key;
            return this;
        }

        @NonNull
        public Builder workoutKey(String workoutKey) {
            this.workoutKey = workoutKey;
            return this;
        }

        @NonNull
        public Builder reaction(@ReactionSummary.ReactionType String reaction) {
            this.reaction = reaction;
            return this;
        }

        @NonNull
        public Builder userName(String userName) {
            this.userName = userName;
            return this;
        }

        @NonNull
        public Builder userRealName(String userRealName) {
            this.userRealName = userRealName;
            return this;
        }

        @NonNull
        public Builder userProfilePictureUrl(String userProfilePictureUrl) {
            this.userProfilePictureUrl = userProfilePictureUrl;
            return this;
        }

        @NonNull
        public Builder timestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        @NonNull
        public Builder deleted(boolean deleted) {
            this.deleted = deleted;
            return this;
        }

        @NonNull
        public Builder locallyChanged(boolean locallyChanged) {
            this.locallyChanged = locallyChanged;
            return this;
        }

        @NonNull
        public Builder synced() {
            this.locallyChanged = false;
            return this;
        }

        @NonNull
        public Reaction build() {
            if (TextUtils.isEmpty(workoutKey)) {
                throw new IllegalStateException("Missing workout key");
            }
            if (TextUtils.isEmpty(userName)) {
                throw new IllegalStateException("Missing user name");
            }
            if (TextUtils.isEmpty(reaction)) {
                throw new IllegalStateException("Missing reaction");
            }
            if (!ReactionSummary.REACTION_LIKE.equals(reaction)) {
                Timber.w("Unsupported reaction: %s", reaction);
            }

            id = (workoutKey + reaction + userName).hashCode();

            return new Reaction(id, key, workoutKey, reaction, userName, userRealName,
                userProfilePictureUrl, timestamp, locallyChanged, deleted);
        }
    }
}
