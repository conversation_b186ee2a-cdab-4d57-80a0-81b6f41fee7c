package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.stt.android.data.source.local.fitnessextension.FitnessExtensionDao
import com.stt.android.data.source.local.fitnessextension.LocalFitnessExtension
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.db.containsTable
import com.stt.android.db.getFloatOrNull
import com.stt.android.db.getInt
import com.stt.android.db.getIntOrNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import timber.log.Timber

class DatabaseUpgrade59To60Helper(
    private val db: SQLiteDatabase,
    private val userDao: UserDao,
    private val fitnessExtensionDao: FitnessExtensionDao
) {

    fun migrateToRoom() {
        // If user isn't logged in, no need to migrate
        val currentUsername = runBlocking(Dispatchers.IO) {
            userDao.findCurrentUser()?.username
        }

        if (currentUsername == null) {
            Timber.d("Skipping migrating fitness extension table contents, user logged out")
            removeOldTables()
            return
        }

        migrateFitnessExtensionItems()
        removeOldTables()
    }

    private fun migrateFitnessExtensionItems() {
        if (db.containsTable("fitnessextension")) {
            Timber.i("Migrating fitness extension from old db to room db")
            db.query(
                "fitnessextension",
                null,
                null,
                null,
                null,
                null,
                null
            ).use { itemCursor ->
                val items: List<LocalFitnessExtension> = buildList {
                    if (itemCursor.moveToFirst()) {
                        do {
                            val workoutId = itemCursor.getInt("workoutId")

                            val maxHeartRate = try {
                                itemCursor.getIntOrNull("maxHeartRate")
                            } catch (e: Throwable) {
                                Timber.w(e, "Error getting maxHeartRate for workoutId: $workoutId")
                                null
                            } ?: 0

                            val vo2Max = try {
                                itemCursor.getFloatOrNull("vo2Max")
                            } catch (e: Throwable) {
                                Timber.w(e, "Error getting vo2Max for workoutId: $workoutId")
                                null
                            } ?: 0.0f

                            val fitnessAge = try {
                                itemCursor.getIntOrNull("fitnessAge")
                            } catch (e: Throwable) {
                                Timber.w(e, "Error getting fitnessAge for workoutId: $workoutId")
                                null
                            }

                            add(
                                LocalFitnessExtension(
                                    workoutId = workoutId,
                                    maxHeartRate = maxHeartRate,
                                    vo2Max = vo2Max,
                                    fitnessAge = fitnessAge
                                )
                            )
                        } while (itemCursor.moveToNext())
                    }
                }

                runBlocking(Dispatchers.IO) {
                    fitnessExtensionDao.upsert(items)
                }
            }
        }
    }

    private fun removeOldTables() {
        Timber.i("Dropping old fitness extension table (table name fitnessextension")
        db.execSQL("DROP TABLE IF EXISTS fitnessextension")
    }
}
