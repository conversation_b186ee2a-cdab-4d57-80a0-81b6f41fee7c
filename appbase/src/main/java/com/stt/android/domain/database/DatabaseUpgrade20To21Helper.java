package com.stt.android.domain.database;

import android.database.sqlite.SQLiteDatabase;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary;
import java.sql.SQLException;

public class DatabaseUpgrade20To21Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade20To21Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, SlopeSkiSummary.TABLE_NAME,
            SlopeSkiSummary.DbFields.MAX_SPEED);
    }
}
