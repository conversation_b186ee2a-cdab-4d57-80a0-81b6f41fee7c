package com.stt.android.domain.database.deprecated;

import android.provider.BaseColumns;

/**
 * This class is used by old DB migrations
 * @deprecated Please use {@link com.stt.android.data.routes.Route} instead
 */
@Deprecated
public final class OldRouteTable {
    public static final String TABLE_NAME = "routes";
    public static abstract class DbFields {
        public static final String ID = BaseColumns._ID;
        public static final String KEY = "key";
        public static final String OWNER_USER_NAME = "ownerUserName";
        public static final String NAME = "name";
        public static final String VISIBILITY = "visibility";
        public static final String ACTIVITY_IDS = "activityIds";
        public static final String AVERAGE_SPEED = "avgSpeed";
        public static final String TOTAL_DISTANCE = "totalDistance";
        public static final String TOTAL_ASCENT = "totalAscent";
        public static final String START_POINT = "startPoint";
        public static final String CENTER_POINT = "centerPoint";
        public static final String STOP_POINT = "stopPoint";
        public static final String LOCALLY_CHANGED = "locallyChanged";
        public static final String DELETED = "deleted";
        public static final String CREATED = "created";
        public static final String WATCH_SYNC_STATE = "watchSyncState";
        public static final String WATCH_SYNC_RESPONSE_CODE = "watchSyncResponseCode";
        public static final String SEGMENTS = "segments";
        public static final String WATCH_ROUTE_ID = "watchRouteId";
        public static final String WATCH_ENABLED = "watchEnabled";
    }
}
