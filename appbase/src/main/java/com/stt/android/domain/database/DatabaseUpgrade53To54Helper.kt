package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import com.j256.ormlite.support.ConnectionSource
import com.stt.android.domain.user.LegacyWorkoutHeader
import timber.log.Timber
import java.sql.SQLException

class DatabaseUpgrade53To54Helper(
    db: SQLiteDatabase,
    connectionSource: ConnectionSource,
    databaseHelper: DatabaseHelper
) : DatabaseUpgradeHelper(db, connectionSource, databaseHelper) {
    @Throws(SQLException::class)
    override fun upgrade() {
        Timber.d("Adding tssList to WorkoutHeader DB table")
        DatabaseHelper.addColumnIfNotExist(
            db,
            LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.TSS_LIST
        )
    }
}
