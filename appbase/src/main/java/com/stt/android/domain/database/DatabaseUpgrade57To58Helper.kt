package com.stt.android.domain.database

import android.database.sqlite.SQLiteDatabase
import android.provider.BaseColumns
import com.stt.android.billing.Purchase
import com.stt.android.data.source.local.billing.LocalPendingPurchase
import com.stt.android.data.source.local.billing.LocalSubscriptionInfo
import com.stt.android.data.source.local.billing.LocalSubscriptionItem
import com.stt.android.data.source.local.billing.PendingPurchaseDao
import com.stt.android.data.source.local.billing.SubscriptionInfoDao
import com.stt.android.data.source.local.billing.SubscriptionItemDao
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.db.containsTable
import com.stt.android.db.getBlob
import com.stt.android.db.getInt
import com.stt.android.db.getLong
import com.stt.android.db.getLongOrNull
import com.stt.android.db.getString
import com.stt.android.domain.user.UserSubscription
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.io.ObjectInputStream

class DatabaseUpgrade57To58Helper(
    private val db: SQLiteDatabase,
    private val userDao: UserDao,
    private val itemDao: SubscriptionItemDao,
    private val subscriptionInfoDao: SubscriptionInfoDao,
    private val pendingPurchaseDao: PendingPurchaseDao
) {

    fun migrateToRoom() {
        // If user isn't logged in, no need to migrate
        val currentUsername = runBlocking(Dispatchers.IO) {
            userDao.findCurrentUser()?.username
        }

        if (currentUsername == null) {
            Timber.d("Skipping migrating ST premium table contents, user logged out")
            removeOldTables()
            return
        }

        migrateSubscriptionInfos()
        migrateSubscriptionItems()
        migratePendingPurchases()
        removeOldTables()
    }

    private fun migrateSubscriptionInfos() {
        if (db.containsTable(OldSubscriptionInfoSchema.TABLE_NAME)) {
            Timber.i("Migrating subscription info from old db to room db")
            db.query(
                OldSubscriptionInfoSchema.TABLE_NAME,
                null,
                null,
                null,
                null,
                null,
                null
            ).use { infoCursor ->
                val infoList = buildList {
                    if (infoCursor.moveToFirst()) {
                        do {
                            add(
                                LocalSubscriptionInfo(
                                    id = infoCursor.getString(OldSubscriptionInfoSchema.COLUMN_ID),
                                    type = LocalSubscriptionInfo.SubscriptionType.valueOf(
                                        infoCursor.getString(OldSubscriptionInfoSchema.COLUMN_TYPE)
                                    ),
                                    length = LocalSubscriptionInfo.SubscriptionLength.valueOf(
                                        infoCursor.getString(OldSubscriptionInfoSchema.COLUMN_LENGTH)
                                    ),
                                    autoRenew = infoCursor.getInt(OldSubscriptionInfoSchema.COLUMN_AUTO_RENEW) != 0,
                                    localizedPrice = infoCursor.getString(OldSubscriptionInfoSchema.COLUMN_LOCALIZED_PRICE),
                                    fetchedTimestamp = infoCursor.getLong(OldSubscriptionInfoSchema.COLUMN_FETCHED_TIMESTAMP),
                                    freeTrialPeriodSeconds = infoCursor.getLongOrNull(OldSubscriptionInfoSchema.COLUMN_FREE_TRIAL_PERIOD_SECONDS)
                                )
                            )
                        } while (infoCursor.moveToNext())
                    }
                }

                runBlocking(Dispatchers.IO) {
                    subscriptionInfoDao.upsert(infoList)
                }
            }
        }
    }

    private fun migrateSubscriptionItems() {
        if (db.containsTable(OldSubscriptionItemSchema.TABLE_NAME)) {
            Timber.i("Migrating subscription item from old db to room db")
            db.query(
                OldSubscriptionItemSchema.TABLE_NAME,
                null,
                null,
                null,
                null,
                null,
                null
            ).use { itemCursor ->
                val items: List<LocalSubscriptionItem> = buildList {
                    if (itemCursor.moveToFirst()) {
                        do {
                            val id = itemCursor.getInt(OldSubscriptionItemSchema.COLUMN_ID)
                            val serializedUserSubscription =
                                itemCursor.getBlob(OldSubscriptionItemSchema.COLUMN_CONTENT)
                            val userSubscription = ObjectInputStream(
                                serializedUserSubscription.inputStream()
                            ).readObject() as UserSubscription

                            if (userSubscription.type != null && userSubscription.length != null) {
                                // If the type or length are null, we can't assume any default value for it;
                                // The local subscription will be updated in next startup, check StartupSync$fetchUserSubscriptions
                                add(
                                    LocalSubscriptionItem(
                                        id = id,
                                        type = LocalSubscriptionInfo.SubscriptionType.valueOf(
                                            userSubscription.type.name
                                        ),
                                        length = LocalSubscriptionInfo.SubscriptionLength.valueOf(
                                            userSubscription.length.name
                                        ),
                                        daysLeft = userSubscription.daysLeft,
                                        autoRenew = userSubscription.isAutoRenew
                                    )
                                )
                            }
                        } while (itemCursor.moveToNext())
                    }
                }

                runBlocking(Dispatchers.IO) {
                    itemDao.upsert(items)
                }
            }
        }
    }

    private fun migratePendingPurchases() {
        if (db.containsTable(OldPendingPurchaseSchema.TABLE_NAME)) {
            Timber.i("Migrating pending purchases from old db to room db")
            db.query(
                OldPendingPurchaseSchema.TABLE_NAME,
                null,
                null,
                null,
                null,
                null,
                null
            ).use { ppCursor ->
                val pendingPurchases = buildList {
                    if (ppCursor.moveToFirst()) {
                        do {
                            val id = ppCursor.getString(OldPendingPurchaseSchema.COLUMN_ID)
                            val serializedPurchase =
                                ppCursor.getBlob(OldPendingPurchaseSchema.COLUMN_CONTENT)
                            val purchase = ObjectInputStream(
                                serializedPurchase.inputStream()
                            ).readObject() as Purchase

                            add(
                                LocalPendingPurchase(
                                    id = id,
                                    itemType = purchase.itemType,
                                    signature = purchase.signature,
                                    originalJson = purchase.originalJson,
                                    orderId = purchase.orderId,
                                    packageName = purchase.packageName,
                                    sku = purchase.sku,
                                    purchaseTime = purchase.purchaseTime,
                                    purchaseState = purchase.purchaseState,
                                    developerPayload = purchase.developerPayload,
                                    token = purchase.token
                                )
                            )
                        } while (ppCursor.moveToNext())
                    }
                }

                runBlocking(Dispatchers.IO) {
                    pendingPurchaseDao.upsert(pendingPurchases)
                }
            }
        }
    }

    private fun removeOldTables() {
        Timber.i("Dropping old subscription info table (table name ${OldSubscriptionInfoSchema.TABLE_NAME})")
        db.execSQL("DROP TABLE IF EXISTS ${OldSubscriptionInfoSchema.TABLE_NAME}")

        Timber.i("Dropping old subscription item table (table name ${OldSubscriptionItemSchema.TABLE_NAME})")
        db.execSQL("DROP TABLE IF EXISTS ${OldSubscriptionItemSchema.TABLE_NAME}")

        Timber.i("Dropping old pending purchase table (table name ${OldPendingPurchaseSchema.TABLE_NAME})")
        db.execSQL("DROP TABLE IF EXISTS ${OldPendingPurchaseSchema.TABLE_NAME}")
    }

    companion object {
        private object OldSubscriptionInfoSchema {
            const val TABLE_NAME = "subscriptioninfo"
            const val COLUMN_ID = BaseColumns._ID
            const val COLUMN_TYPE = "type"
            const val COLUMN_LENGTH = "length"
            const val COLUMN_AUTO_RENEW = "autoRenew"
            const val COLUMN_LOCALIZED_PRICE = "localizedPrice"
            const val COLUMN_FETCHED_TIMESTAMP = "fetchedTimestamp"
            const val COLUMN_FREE_TRIAL_PERIOD_SECONDS = "freeTrialPeriodSeconds"
        }

        private object OldSubscriptionItemSchema {
            const val TABLE_NAME = "s"
            const val COLUMN_ID = BaseColumns._ID
            const val COLUMN_CONTENT = "c"
        }

        private object OldPendingPurchaseSchema {
            const val TABLE_NAME = "i"
            const val COLUMN_ID = BaseColumns._ID
            const val COLUMN_CONTENT = "c"
        }
    }
}
