package com.stt.android.domain.database;

import androidx.annotation.NonNull;
import com.j256.ormlite.field.FieldType;
import com.j256.ormlite.field.SqlType;
import com.j256.ormlite.field.types.StringType;
import com.j256.ormlite.support.DatabaseResults;
import com.squareup.moshi.JsonAdapter;
import com.stt.android.domain.Point;
import com.stt.android.moshi.MoshiProvider;
import java.io.IOException;
import java.sql.SQLException;

/**
 * Persists {@link Point} as JSON string to DB
 */
public class JsonPointPersister extends StringType {
    private static JsonPointPersister singleton = new JsonPointPersister();
    private static JsonAdapter<Point> adapter = MoshiProvider.INSTANCE.getInstance().adapter(Point.class);

    public JsonPointPersister() {
        super(SqlType.STRING, new Class<?>[]{Point.class});
    }

    public static JsonPointPersister getSingleton() {
        return singleton;
    }

    @Override
    public Object sqlArgToJava(FieldType fieldType, Object sqlArg, int columnPos) {
        return sqlArg;
    }

    @Override
    public Object resultToSqlArg(FieldType fieldType, DatabaseResults results, int columnPos) throws SQLException {
        try {
            return stringToPoint(results.getString(columnPos));
        }
        catch (IOException e) {
            throw new SQLException("Unable to deserialize json", e);
        }
    }

    public static Point stringToPoint(String json) throws IOException {
        return adapter.fromJson(json);
    }

    @Override
    public Object javaToSqlArg(FieldType fieldType, Object javaObject) {
        Point point = (Point) javaObject;
        return pointToString(point);
    }

    @NonNull
    public static String pointToString(Point point) {
        return adapter.toJson(point);
    }
}
