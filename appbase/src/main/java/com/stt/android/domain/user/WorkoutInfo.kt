package com.stt.android.domain.user

import com.stt.android.domain.ranking.Ranking
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.workoutdetail.comments.WorkoutComment

data class WorkoutInfo internal constructor(
    val workoutHeader: WorkoutHeader,
    val comments: List<WorkoutComment>,
    val images: List<ImageInformation>,
    val videos: List<VideoInformation>,
    val reactions: List<ReactionSummary>,
    val extensions: List<WorkoutExtension>,
    val rankings: List<Ranking>
) {
    fun toBuilder(): Builder = Builder(
        workoutHeader = workoutHeader,
        comments = comments,
        images = images,
        videos = videos,
        reactions = reactions,
        extensions = extensions,
        rankings = rankings
    )

    @Suppress("LongParameterList")
    class Builder internal constructor(
        private var workoutHeader: WorkoutHeader? = null,
        private var comments: List<WorkoutComment>? = null,
        private var images: List<ImageInformation>? = null,
        private var videos: List<VideoInformation>? = null,
        private var reactions: List<ReactionSummary>? = null,
        private var extensions: List<WorkoutExtension>? = null,
        private var rankings: List<Ranking>? = null
    ) {
        fun workoutHeader(value: WorkoutHeader): Builder = apply {
            this.workoutHeader = value
        }

        fun comments(value: List<WorkoutComment>): Builder = apply { this.comments = value }

        fun images(value: List<ImageInformation>): Builder = apply { this.images = value }

        fun videos(value: List<VideoInformation>): Builder = apply { this.videos = value }

        fun reactions(value: List<ReactionSummary>): Builder = apply {
            this.reactions = value
        }

        fun extensions(value: List<WorkoutExtension>): Builder = apply {
            this.extensions =
                value
        }

        fun rankings(value: List<Ranking>): Builder = apply { this.rankings = value }

        fun build(): WorkoutInfo = WorkoutInfo(
            workoutHeader = workoutHeader ?: error("workoutHeader == null"),
            comments = comments ?: error("comments == null"),
            images = images ?: error("images == null"),
            videos = videos ?: error("videos == null"),
            reactions = reactions ?: error("reactions == null"),
            extensions = extensions ?: error("extensions == null"),
            rankings = rankings ?: error("rankings == null")
        )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
