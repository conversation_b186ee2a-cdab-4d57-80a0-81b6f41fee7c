package com.stt.android.domain.database;

import android.content.Context;
import android.content.SharedPreferences;
import android.database.sqlite.SQLiteDatabase;
import androidx.preference.PreferenceManager;
import com.j256.ormlite.support.ConnectionSource;
import com.stt.android.STTApplication;
import com.stt.android.domain.user.LegacyWorkoutHeader;
import com.stt.android.utils.DateUtils;
import com.stt.android.utils.STTConstants;
import java.sql.SQLException;

public class DatabaseUpgrade23To24Helper extends DatabaseUpgradeHelper {
    public DatabaseUpgrade23To24Helper(SQLiteDatabase db, ConnectionSource connectionSource,
        DatabaseHelper databaseHelper) {
        super(db, connectionSource, databaseHelper);
    }

    @Override
    public void upgrade() throws SQLException {
        DatabaseHelper.addColumnIfNotExist(db, LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.TOTAL_ASCENT);
        DatabaseHelper.addColumnIfNotExist(db, LegacyWorkoutHeader.TABLE_NAME,
            LegacyWorkoutHeader.DbFields.TOTAL_DESCENT);

        // Once we've added the new columns let's modify the timestamp for the latest workouts
        // for current user so we get workout headers with right total ascent/descent from the
        // last 3 weeks
        Context appContext = STTApplication.getComponent().appContext();
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(appContext);
        long ownWorkoutLastModifiedTimestamp = preferences.getLong(
            STTConstants.DefaultPreferences.KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP, 0L);
        if (ownWorkoutLastModifiedTimestamp != 0) {
            ownWorkoutLastModifiedTimestamp -= 3 * DateUtils.WEEK_IN_MILLIS;
            preferences.edit()
                .putLong(
                    STTConstants.DefaultPreferences
                        .KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP,
                    ownWorkoutLastModifiedTimestamp)
                .apply();
        }
    }
}
