package com.stt.android.domain.user.workoutextension;

import androidx.annotation.NonNull;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.annotations.SerializedName;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import java.lang.reflect.Type;

public abstract class BackendWorkoutExtension {
    static final String TYPE_KEY = "type";
    @SerializedName(TYPE_KEY)
    protected final String type;

    protected BackendWorkoutExtension(String type) {
        this.type = type;
    }

    @NonNull
    public abstract WorkoutExtension toWorkoutExtension(int workoutId)
        throws UnsupportedExtensionException;

    public static class UnknownBackendWorkoutExtension extends BackendWorkoutExtension {
        UnknownBackendWorkoutExtension(String type) {
            super(type);
        }

        @NonNull
        @Override
        public WorkoutExtension toWorkoutExtension(int workoutId)
            throws UnsupportedExtensionException {
            throw new UnsupportedExtensionException("Unknown extension type: " + type);
        }
    }

    public static class Deserializer implements JsonDeserializer<BackendWorkoutExtension> {
        @Override
        public BackendWorkoutExtension deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws JsonParseException {
            JsonObject jsonObject = json.getAsJsonObject();
            String type = context.deserialize(jsonObject.get(TYPE_KEY), String.class);
            switch (type) {
                case BackendSlopeSkiWorkoutExtension.TYPE:
                    return context.deserialize(json, BackendSlopeSkiWorkoutExtension.class);
                case BackendWorkoutSummaryExtension.TYPE:
                    return context.deserialize(json, BackendWorkoutSummaryExtension.class);
                case BackendFitnessExtension.TYPE:
                    return context.deserialize(json, BackendFitnessExtension.class);
                case BackendIntensityExtension.TYPE:
                    return context.deserialize(json, BackendIntensityExtension.class);
                case BackendDiveHeaderExtension.TYPE:
                    return context.deserialize(json, BackendDiveHeaderExtension.class);
                case BackendSwimmingHeaderExtension.TYPE:
                    return context.deserialize(json, BackendSwimmingHeaderExtension.class);
                default:
                    return new UnknownBackendWorkoutExtension(type);
            }
        }
    }

    public class UnsupportedExtensionException extends Exception {
        public UnsupportedExtensionException(String message) {
            super(message);
        }
    }
}
