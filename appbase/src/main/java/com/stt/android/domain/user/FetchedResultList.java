package com.stt.android.domain.user;

import java.util.List;

public class FetchedResultList<T> {
    private final List<T> result;
    private final long lastModified;

    public FetchedResultList(List<T> result, long lastModified) {
        this.result = result;
        this.lastModified = lastModified;
    }

    public List<T> getResult() {
        return result;
    }

    public long getLastModified() {
        return lastModified;
    }
}
