package com.stt.android.domain.user.workoutextension

import com.google.gson.annotations.SerializedName
import com.stt.android.domain.workouts.extensions.DiveExtension

class BackendDiveHeaderExtension(
    @SerializedName("workoutId") var workoutId: Int = 0,
    @SerializedName("maxDepth") var maxDepth: Float? = null,
    @SerializedName("algorithm") var algorithm: String? = null,
    @SerializedName("personalSetting") var personalSetting: Int? = null,
    @SerializedName("diveNumberInSeries") var diveNumberInSeries: Int? = null,
    @SerializedName("cns") var cns: Float? = null,
    @SerializedName("algorithmLock") var algorithmLock: Boolean? = null,
    @SerializedName("diveMode") var diveMode: String? = null,
    @SerializedName("otu") var otu: Float? = null,
    @SerializedName("pauseDuration") var pauseDuration: Float? = null,
    @SerializedName("gasConsumption") var gasConsumption: Float? = null,
    @SerializedName("altitudeSetting") var altitudeSetting: Float? = null,
    // known typo in backend
    @SerializedName("gasQuantitites") var gasQuantities: Map<String, Float?>? = null,
    @SerializedName("surfaceTime") var surfaceTime: Float? = null,
    @SerializedName("diveTime") var diveTime: Float? = null,
    @SerializedName("gasesUsed") var gasesUsed: List<String>? = null,
    @SerializedName("maxDepthTemperature") var maxDepthTemperature: Float? = null,
    @SerializedName("avgDepth") var avgDepth: Float? = null,
    @SerializedName("minGF") var minGF: Float? = null,
    @SerializedName("maxGF") var maxGF: Float? = null
) : BackendWorkoutExtension(TYPE) {

    override fun toWorkoutExtension(workoutId: Int) = DiveExtension(
        workoutId,
        maxDepth,
        algorithm,
        personalSetting,
        diveNumberInSeries,
        cns,
        algorithmLock,
        diveMode,
        otu,
        pauseDuration,
        gasConsumption,
        altitudeSetting,
        gasQuantities ?: mapOf(),
        surfaceTime,
        diveTime,
        gasesUsed ?: listOf(),
        maxDepthTemperature,
        avgDepth,
        minGF,
        maxGF
    )

    companion object {
        const val TYPE = "DiveHeaderExtension"
    }
}
