package com.stt.android.domain.user;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.AutoPause;
import com.stt.android.domain.workout.SpeedPaceState;
import com.stt.android.utils.STTConstants;

public class ActivityTypeHelper {
    private static final String ACTIVITY_AUTO_PAUSE_PREF_PREFIX = "ACTIVITY_AUTO_PAUSE_";
    private static final String ACTIVITY_SPEED_PACE_STATE_PREF_PREFIX =
        "ACTIVITY_SPEED_PACE_STATE_";
    private static final int RECENT_ACTIVITY_LIMIT = 3;

    @NonNull
    public static ActivityType getLastActivity(Context context) {
        return getRecentActivities(context)[0];
    }

    @NonNull
    public static ActivityType[] getRecentActivities(Context context) {
        String recentActivities = PreferenceManager.getDefaultSharedPreferences(context)
            .getString(STTConstants.DefaultPreferences.KEY_RECENT_ACTIVITY_IDS, null);
        if (recentActivities == null) {
            return new ActivityType[] { ActivityType.DEFAULT };
        }

        String[] activityIds = recentActivities.split(":");
        ActivityType[] activityTypes = new ActivityType[activityIds.length];
        for (int i = 0; i < activityIds.length; ++i) {
            activityTypes[i] = ActivityType.valueOf(Integer.parseInt(activityIds[i]));
        }
        return activityTypes;
    }

    public static void saveRecentActivity(Context context, ActivityType activityType) {
        ActivityType[] recentActivities = getRecentActivities(context);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(activityType.getId());
        int count = 1;
        for (ActivityType recentActivity : recentActivities) {
            if (recentActivity != activityType) {
                stringBuilder.append(":").append(recentActivity.getId());
                if (++count == RECENT_ACTIVITY_LIMIT) {
                    break;
                }
            }
        }
        PreferenceManager.getDefaultSharedPreferences(context)
            .edit()
            .putString(STTConstants.DefaultPreferences.KEY_RECENT_ACTIVITY_IDS,
                stringBuilder.toString())
            .apply();
    }

    @NonNull
    public static AutoPause getAutoPause(
        @NonNull Context context,
        @NonNull ActivityType activityType) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        String autoPauseStr =
            prefs.getString(ACTIVITY_AUTO_PAUSE_PREF_PREFIX + activityType.getId(),
                AutoPause.DEFAULT.name());
        return AutoPause.valueOf(autoPauseStr);
    }

    public static void saveAutoPause(Context context, ActivityType activityType,
        AutoPause activityAutoPause) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        prefs.edit()
            .putString(ACTIVITY_AUTO_PAUSE_PREF_PREFIX + activityType.getId(),
                activityAutoPause.name())
            .apply();
    }

    @NonNull
    public static SpeedPaceState getSpeedPaceState(
        boolean useUserState,
        @NonNull Context context,
        @NonNull ActivityType activityType
    ) {
        if (useUserState) {
            return getUserSpeedPaceState(context, activityType);
        } else {
            return getDefaultSpeedPaceState(activityType);
        }
    }

    @NonNull
    public static SpeedPaceState getDefaultSpeedPaceState(@NonNull ActivityType activityType) {
        SpeedPaceState defaultSpeedPaceState = SpeedPaceState.DEFAULT;
        if (activityType.equals(ActivityType.RUNNING) || activityType.equals(ActivityType.TRAIL_RUNNING) || activityType.equals(ActivityType.TRACK_RUNNING)) {
            defaultSpeedPaceState = SpeedPaceState.PACE;
        } else if (activityType.equals(ActivityType.SAILING)) {
            defaultSpeedPaceState = SpeedPaceState.SPEEDKNOTS;
        }
        return defaultSpeedPaceState;
    }

    /**
     * Used in screens related to recording workouts with app, use the default in other places
     */
    @NonNull
    public static SpeedPaceState getUserSpeedPaceState(Context context,
        @NonNull ActivityType activityType) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SpeedPaceState defaultSpeedPaceState = getDefaultSpeedPaceState(activityType);
        String speedPaceState =
            prefs.getString(ACTIVITY_SPEED_PACE_STATE_PREF_PREFIX + activityType.getId(),
                defaultSpeedPaceState.name());
        return SpeedPaceState.valueOf(speedPaceState);
    }

    public static void saveUserSpeedState(Context context, ActivityType activityType,
        SpeedPaceState speedPaceState) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        prefs.edit()
            .putString(ACTIVITY_SPEED_PACE_STATE_PREF_PREFIX + activityType.getId(),
                speedPaceState.name())
            .apply();
    }
}
