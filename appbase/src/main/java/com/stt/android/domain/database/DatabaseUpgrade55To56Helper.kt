@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.stt.android.data.source.local.user.LocalUser
import com.stt.android.data.source.local.user.LocalUserSession
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.db.getBlobOrNull
import com.stt.android.db.getInt
import com.stt.android.db.getIntOrNull
import com.stt.android.db.getString
import com.stt.android.db.getStringOrNull
import com.stt.android.domain.UserSession
import com.stt.android.domain.user.LegacyUser
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.io.ByteArrayInputStream
import java.io.ObjectInputStream

class DatabaseUpgrade55To56Helper(
    db: SQLiteDatabase,
    dao: UserDao
) : RoomMigration<LocalUser, UserDao>(db, dao, LegacyUser.TABLE_NAME) {
    override fun insertDataEntities(dao: UserDao, dataEntities: List<LocalUser>) {
        runBlocking(IO) {
            // Use upsert to work around 'UNIQUE constraint failed' exceptions
            // for some users. Migration may be triggered twice under mysterious conditions
            // probably related to multiple processes.
            dao.upsert(dataEntities)
        }
    }

    override fun mapToDataEntity(cursor: Cursor) =
        LocalUser(
            id = cursor.getInt(LegacyUser.DbFields.ID),
            key = cursor.getStringOrNull(LegacyUser.DbFields.KEY),
            username = cursor.getString(LegacyUser.DbFields.USERNAME),
            session = cursor.getBlobOrNull(LegacyUser.DbFields.SESSION)?.deserializeUserSession(),
            website = cursor.getStringOrNull(LegacyUser.DbFields.WEBSITE),
            city = cursor.getStringOrNull(LegacyUser.DbFields.CITY),
            country = cursor.getStringOrNull(LegacyUser.DbFields.COUNTRY),
            profileImageUrl = cursor.getStringOrNull(LegacyUser.DbFields.PROFILE_IMAGE_URL),
            profileImageKey = cursor.getStringOrNull(LegacyUser.DbFields.PROFILE_IMAGE_KEY),
            realName = cursor.getStringOrNull(LegacyUser.DbFields.REAL_NAME),
            // We may or may not have 'description' column in the database depending on the
            // migration history
            description = cursor.getStringOrNull(LegacyUser.DbFields.DESCRIPTION, throwIfColumnMissing = false),
            followModel = cursor.getIntOrNull(LegacyUser.DbFields.FOLLOW_MODEL) == 1,
            fieldTester = cursor.getIntOrNull(LegacyUser.DbFields.FIELD_TESTER) == 1,
            createdDate = null,
            lastLogin = null,
            roles = null,
            coverImageUrl = null,
            showLocale = null,
            blocked = null,
        )
}

private fun ByteArray.deserializeUserSession(): LocalUserSession? =
    ObjectInputStream(ByteArrayInputStream(this)).use {
        try {
            val userSession = it.readObject() as UserSession
            LocalUserSession(
                sessionKey = userSession.sessionKey,
                facebookConnected = userSession.isConnectedToFacebook,
                emailVerified = null,
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to read user session from db (this=$this)")
            null
        }
    }
