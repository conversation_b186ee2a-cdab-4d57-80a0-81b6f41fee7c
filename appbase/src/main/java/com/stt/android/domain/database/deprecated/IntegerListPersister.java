package com.stt.android.domain.database.deprecated;

import com.j256.ormlite.field.FieldType;
import com.j256.ormlite.field.SqlType;
import com.j256.ormlite.field.types.ByteArrayType;
import com.j256.ormlite.support.DatabaseResults;
import com.stt.android.domain.database.JsonIntegerListPersister;
import static com.stt.android.domain.database.MigrationUtil.byteArrayToIntList;
import static com.stt.android.domain.database.MigrationUtil.intListToByteArray;
import java.sql.SQLException;
import java.util.List;

/**
 * The IntegerListPersister class is used to persist an integer list to the database as byte array.
 * @deprecated This persister should not be used anymore. Instead use {@link JsonIntegerListPersister}
 */
@Deprecated
public class IntegerListPersister extends ByteArrayType {
    private static IntegerListPersister singleton = new IntegerListPersister();

    public IntegerListPersister() {
        super(SqlType.BYTE_ARRAY, new Class<?>[]{List.class});
    }

    public static IntegerListPersister getSingleton() {
        return singleton;
    }

    @Override
    public Object sqlArgToJava(FieldType fieldType, Object sqlArg, int columnPos) throws SQLException {
        return sqlArg;
    }

    @Override
    public Object resultToSqlArg(FieldType fieldType, DatabaseResults results, int columnPos) throws SQLException {
        byte[] bytes = results.getBytes(columnPos);
        return byteArrayToIntList(bytes);
    }

    @Override
    public Object javaToSqlArg(FieldType fieldType, Object javaObject) throws SQLException {
        return intListToByteArray((List<Integer>) javaObject);
    }
}
