package com.stt.android.domain.sml

import com.stt.android.data.source.local.smljson.SMLFileStorage
import com.stt.android.data.source.local.smlzip.SMLZipReferenceDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * Deletes SML data from local database
 */
class DeleteSmlDataUseCase
@Inject constructor(
    private val smlFileStorage: SMLFileStorage,
    private val smlZipReferenceDao: SMLZipReferenceDao
) {
    suspend operator fun invoke(workoutId: Int) = withContext(Dispatchers.IO) {
        try {
            val smlZipReference = smlZipReferenceDao.findById(workoutId)
            if (smlZipReference != null) {
                smlZipReferenceDao.remove(smlZipReference)
            }
        } catch (e: Exception) {
            Timber.w(e, "Error deleting sml zip reference")
        }

        try {
            smlFileStorage.deleteSmlFileIfExists(workoutId)
        } catch (e: Exception) {
            Timber.w(e, "Error deleting local sml extension")
        }
    }
}
