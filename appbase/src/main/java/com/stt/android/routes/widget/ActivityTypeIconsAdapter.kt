package com.stt.android.routes.widget

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.IntRange
import androidx.core.content.ContextCompat
import androidx.core.widget.ImageViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.databinding.ItemActivityIconsImageviewBinding
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.home.diary.diarycalendar.activitygroups.colorRes
import com.stt.android.core.R as CR

class ActivityTypeIconsAdapter @JvmOverloads constructor(
    private val activities: List<ActivityType>,
    private val background: Background = Background.NONE,
    @IntRange(from = 1) private var visibleActivityIconLimit: Int = Int.MAX_VALUE,
    private val expandable: Boolean = true,
    private val activityGroupMapper: ActivityGroupMapper? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    enum class Background {
        NONE,
        GRAY,
        COLORED
    }

    val expandIconList: (position: Int) -> Unit = {
        visibleActivityIconLimit = Int.MAX_VALUE
        @Suppress("NotifyDataSetChanged")
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            R.layout.item_activity_icons_imageview -> {
                val binding = ItemActivityIconsImageviewBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ImageViewHolder(binding, expandIconList)
            }
            else -> TextViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_activity_icons_textview, parent, false),
                expandIconList
            )
        }
    }

    override fun getItemCount(): Int {
        return if (visibleActivityIconLimit < activities.size) visibleActivityIconLimit + 1 else activities.size
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is TextViewHolder -> holder.bind(activities.size, visibleActivityIconLimit, expandable)
            is ImageViewHolder -> {
                val activity = activities[position]
                val colorRes = activityGroupMapper?.activityTypeIdToGroup(activity.id)?.colorRes
                holder.bind(activity.iconId, colorRes, background, expandable)
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position < visibleActivityIconLimit) R.layout.item_activity_icons_imageview else R.layout.item_activity_icons_textview
    }
}

private abstract class ActivityTypeViewHolder(itemView: View, private val listener: (position: Int) -> Unit) :
    RecyclerView.ViewHolder(itemView), View.OnClickListener {

    fun bind(expandable: Boolean) {
        if (expandable) {
            itemView.setOnClickListener(this)
        }
    }

    override fun onClick(view: View) {
        listener.invoke(bindingAdapterPosition)
    }
}

private class ImageViewHolder(
    val binding: ItemActivityIconsImageviewBinding,
    listener: (position: Int) -> Unit
) : ActivityTypeViewHolder(binding.root, listener) {

    fun bind(
        @DrawableRes iconRes: Int,
        @ColorRes activityGroupColor: Int?,
        background: ActivityTypeIconsAdapter.Background,
        expandable: Boolean
    ) {
        super.bind(expandable)
        val context = itemView.context

        @ColorInt
        val iconTintColor: Int

        @ColorInt
        val backgroundTintColor: Int?

        when (background) {
            ActivityTypeIconsAdapter.Background.NONE -> {
                // No background
                iconTintColor = ThemeColors.primaryTextColor(itemView.context)
                backgroundTintColor = null
            }

            ActivityTypeIconsAdapter.Background.GRAY -> {
                // Light gray background
                iconTintColor = ThemeColors.primaryTextColor(itemView.context)
                backgroundTintColor =
                    ContextCompat.getColor(context, CR.color.cold_gray_with_transparency)
            }

            ActivityTypeIconsAdapter.Background.COLORED -> {
                // Activity group color based background, white icon
                iconTintColor = ContextCompat.getColor(context, CR.color.white)
                backgroundTintColor =
                    activityGroupColor?.run { ContextCompat.getColor(context, this) }
            }
        }

        binding.activityIcon.setImageResource(iconRes)
        ImageViewCompat.setImageTintList(
            binding.activityIcon,
            ColorStateList.valueOf(iconTintColor)
        )
        if (backgroundTintColor != null) {
            binding.activityGroupBackground.visibility = View.VISIBLE
            ImageViewCompat.setImageTintList(
                binding.activityGroupBackground,
                ColorStateList.valueOf(backgroundTintColor)
            )
        } else {
            binding.activityGroupBackground.visibility = View.GONE
        }
    }
}

private class TextViewHolder(itemView: View, listener: (position: Int) -> Unit) :
    ActivityTypeViewHolder(itemView, listener) {

    @SuppressLint("SetTextI18n")
    fun bind(totalActivities: Int, limit: Int, expandable: Boolean) {
        super.bind(expandable)
        with(itemView as TextView) {
            text = "+${totalActivities - limit}"
            if (expandable) {
                setTextColor(ThemeColors.resolveColor(itemView.context, R.attr.newAccentColor))
            }
        }
    }
}
