package com.stt.android.social.friends.discover

import android.content.res.Configuration.ORIENTATION_PORTRAIT
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.follow.FollowDirection
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.friends.composables.EntryRow
import com.stt.android.social.friends.composables.FriendView

@Composable
fun DiscoverFriendsContent(
    state: DiscoverFriendsState,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    onPhoneContactsClick: () -> Unit,
    onFacebookFriendsClick: () -> Unit,
    onInvitePeopleClick: () -> Unit,
    modifier: Modifier = Modifier,
    facebookLoading: Boolean = false,
) {
    val isPortrait = LocalConfiguration.current.orientation == ORIENTATION_PORTRAIT
    Box(modifier = modifier) {
        LazyColumn(
            contentPadding = PaddingValues(top = MaterialTheme.spacing.medium),
        ) {
            if (state.showPhoneContacts) {
                item(key = "phone_contacts") {
                    EntryRow(
                        icon = R.drawable.phone_outline,
                        title = R.string.phone_contacts,
                        onClick = onPhoneContactsClick,
                    )
                }
            }
            if (state.showFacebookFriends) {
                item(key = "facebook_friends") {
                    EntryRow(
                        icon = R.drawable.facebook_fill,
                        title = R.string.facebook_friends,
                        onClick = onFacebookFriendsClick,
                        loading = facebookLoading,
                    )
                }
            }
            item(key = "invite_people") {
                EntryRow(
                    icon = R.drawable.ic_find_people,
                    title = R.string.invite_friends,
                    onClick = onInvitePeopleClick,
                )
            }
            if (state.friends.any()) {
                item(key = "list_title") {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                    Text(
                        stringResource(R.string.discover_friends_title),
                        style = MaterialTheme.typography.bodyLargeBold,
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    )
                }
                items(items = state.friends, key = { it.username }) { friend ->
                    FriendView(
                        friend = friend,
                        onClick = { onFriendClick(friend) },
                        onStatusClick = { onStatusClick(friend) },
                    )
                }
            } else if (!isPortrait) {
                if (state.loading) {
                    item(key = "loading") {
                        LoadingContent(
                            isLoading = true,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.xlarge),
                        )
                    }
                } else {
                    item(key = "empty_view") {
                        EmptyView(
                            icon = R.drawable.ic_empty_discover,
                            tips = R.string.no_people_discovered,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = MaterialTheme.spacing.xlarge),
                        )
                    }
                }
            }
        }
        if (isPortrait && state.friends.none()) {
            if (state.loading) {
                LoadingContent(
                    isLoading = true,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.fillMaxSize(),
                )
            } else {
                EmptyView(
                    icon = R.drawable.ic_empty_discover,
                    tips = R.string.no_people_discovered,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }
    }
}

@Preview
@Composable
private fun DiscoverFriendsContentPreview() {
    M3AppTheme {
        DiscoverFriendsContent(
            state = DiscoverFriendsState(
                showFacebookFriends = true,
                showPhoneContacts = false,
                friends = FriendStatus.entries.map {
                    Friend(
                        username = "username",
                        realName = "realName",
                        profileDescription = "",
                        profileImageUrl = "",
                        friendStatus = it,
                        followDirection = FollowDirection.FOLLOWING,
                    )
                },
                loading = false,
            ),
            onStatusClick = {},
            onFriendClick = {},
            onPhoneContactsClick = {},
            onFacebookFriendsClick = {},
            onInvitePeopleClick = {},
        )
    }
}

@Preview
@Composable
private fun DiscoverFriendsContentLoadingPreview() {
    M3AppTheme {
        DiscoverFriendsContent(
            state = DiscoverFriendsState(
                showFacebookFriends = true,
                showPhoneContacts = false,
                friends = emptyList(),
                loading = false,
            ),
            onStatusClick = {},
            onFriendClick = {},
            onPhoneContactsClick = {},
            onFacebookFriendsClick = {},
            onInvitePeopleClick = {},
        )
    }
}
