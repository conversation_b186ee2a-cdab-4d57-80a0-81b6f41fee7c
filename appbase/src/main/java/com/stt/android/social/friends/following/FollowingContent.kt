package com.stt.android.social.friends.following

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.follow.FollowDirection
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.friends.composables.FriendView
import com.stt.android.social.friends.composables.FriendsListView

@Composable
fun FollowingContent(
    state: FollowingState,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    modifier: Modifier = Modifier,
    isOthers: Boolean = false,
) {
    when {
        state.requestedFriends.any() && state.following.none() -> {
            LazyColumn(modifier = modifier) {
                items(state.requestedFriends, key = { it.toString() }) {
                    FriendView(
                        friend = it,
                        onClick = { onFriendClick(it) },
                        onStatusClick = { onStatusClick(it) },
                    )
                }

                item {
                    EmptyView(
                        icon = R.drawable.ic_empty_friends,
                        tips = R.string.no_following_new,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 155.dp),
                    )
                }
            }
        }

        else -> {
            FriendsListView(
                friends = state.following,
                onFriendClick = onFriendClick,
                onStatusClick = onStatusClick,
                modifier = modifier,
                headerView = {
                    if (!isOthers) {
                        items(state.requestedFriends, key = { it.toString() }) {
                            FriendView(
                                friend = it,
                                onClick = { onFriendClick(it) },
                                onStatusClick = { onStatusClick(it) },
                            )
                        }
                        item(key = "list_title_${state.followingCount}") {
                            Text(
                                stringResource(R.string.my_following_prefix, state.followingCount),
                                style = MaterialTheme.typography.bodyLargeBold,
                                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                            )
                        }
                    }
                },
                emptyView = {
                    if (state.loading) {
                        LoadingContent(
                            isLoading = true,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.fillMaxSize(),
                        )
                    } else {
                        EmptyView(
                            icon = R.drawable.ic_empty_friends,
                            tips = if (isOthers) R.string.no_people_found else R.string.no_following_new,
                            modifier = Modifier.fillMaxSize(),
                        )
                    }
                },
            )
        }
    }
}

@Preview
@Composable
private fun FollowingScreenPreview() {
    M3AppTheme {
        FollowingContent(
            state = FollowingState(
                friends = listOf(
                    Friend(
                        username = "username",
                        realName = "realName",
                        profileDescription = "",
                        profileImageUrl = "",
                        friendStatus = FriendStatus.REQUESTED,
                        followDirection = FollowDirection.FOLLOWING,
                    ),
                ),
                loading = true,
            ),
            onFriendClick = {},
            onStatusClick = {},
        )
    }
}
