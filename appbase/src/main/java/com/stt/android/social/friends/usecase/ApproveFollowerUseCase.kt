package com.stt.android.social.friends.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.awaitSuspend
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import kotlinx.coroutines.withContext
import javax.inject.Inject

class ApproveFollowerUseCase @Inject constructor(
    private val peopleController: PeopleController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(friend: Friend) {
        withContext(coroutinesDispatchers.io) {
            peopleController.acceptFollower(peopleController.loadUfsFromDbForFollower(friend))
                .awaitSuspend()
        }
    }
}
