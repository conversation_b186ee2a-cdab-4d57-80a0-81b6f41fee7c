package com.stt.android.social.friends.utils

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.social.friends.FriendStatus

@get:StringRes
val FriendStatus.titleRes: Int
    get() = when (this) {
        FriendStatus.REQUESTED -> R.string.requested
        FriendStatus.FOLLOW -> R.string.follow
        FriendStatus.FOLLOWING -> R.string.following
        FriendStatus.FRIEND -> R.string.friends
        FriendStatus.ME -> 0
    }

@get:DrawableRes
val FriendStatus.titlePrefixIconRes: Int?
    get() = when (this) {
        FriendStatus.REQUESTED -> null
        FriendStatus.FOLLOW -> null
        FriendStatus.FOLLOWING -> R.drawable.icon_check
        FriendStatus.FRIEND -> R.drawable.ic_followers_12
        FriendStatus.ME -> null
    }
