package com.stt.android.social.workoutlistv2.ui

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.paging.compose.LazyPagingItems
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.widgets.CustomHeightTabIndicator
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.social.workoutlistv2.WorkoutMediaItem
import com.stt.android.social.workoutlistv2.WorkoutCardItem
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import kotlinx.coroutines.launch

private val TOP_BAR_HEIGHT = 52.dp
private val TAB_HEIGHT = 48.dp

@Composable
fun WorkoutPageTabContent(
    workoutPagingItems: LazyPagingItems<WorkoutCardItem>,
    mediaPagingItems: LazyPagingItems<WorkoutMediaItem>,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    modifier: Modifier = Modifier,
    workoutSummaryStats: WorkoutSummaryStats? = null,
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
    onMediaIndexSelected: (Int) -> Unit = {},
    nestedScrollConnection: NestedScrollConnection? = null,
    updatedCardList: List<WorkoutCardInfo> = emptyList(),
    onSearchClick: () -> Unit = {},
) {
    val noShowingWorkout = workoutPagingItems.itemCount == 0
    val noWorkout = noShowingWorkout && (workoutSummaryStats == null || workoutSummaryStats.totalNumberOfWorkoutsSum == 0)
    val hasMedia = mediaPagingItems.itemCount > 0
    val tabs = if (hasMedia) {
        Tab.entries.toTypedArray()
    } else {
        Tab.entries.take(1).toTypedArray()
    }
    val pagerState = rememberPagerState(initialPage = 0) { tabs.size }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        if (!noWorkout) {
            Tab(
                tabs = tabs,
                pagerState = pagerState,
                hasSearchIcon = !noShowingWorkout,
                onSearchClick = onSearchClick,
            )
        }
        Pager(
            workoutPagingItems = workoutPagingItems,
            mediaPagingItems = mediaPagingItems,
            workoutCardActionsHandler = workoutCardActionsHandler,
            tabs = tabs,
            pagerState = pagerState,
            onMediaIndexSelected = onMediaIndexSelected,
            nestedScrollConnection = nestedScrollConnection,
            updatedCardList = updatedCardList,
            workoutSummaryStats = workoutSummaryStats,
            measurementUnit = measurementUnit,
        )
    }
}

@Composable
private fun Tab(
    tabs: Array<Tab>,
    modifier: Modifier = Modifier,
    pagerState: PagerState = rememberPagerState { 2 },
    hasSearchIcon: Boolean = false,
    onSearchClick: () -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage.coerceIn(0, tabs.lastIndex),
                modifier = Modifier
                    .weight(1f)
                    .height(TAB_HEIGHT),
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
                edgePadding = 0.dp,
                divider = {},
                indicator = { tabPositions ->
                    CustomHeightTabIndicator(
                        tabPositions = tabPositions,
                        selectedTabIndex = pagerState.currentPage,
                    )
                },
            ) {
                tabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = stringResource(tab.title),
                                style = MaterialTheme.typography.bodyLargeBold,
                            )
                        },
                        selectedContentColor = MaterialTheme.colorScheme.primary,
                        unselectedContentColor = MaterialTheme.colorScheme.darkGrey,
                    )
                }
            }

            if (hasSearchIcon) {
                IconButton(
                    onClick = onSearchClick,
                ) {
                    Icon(
                        painter = painterResource(com.stt.android.compose.ui.R.drawable.search_outline),
                        contentDescription = stringResource(R.string.search),
                    )
                }
            }
        }
        HorizontalDivider()
    }
}

@Composable
private fun Pager(
    workoutPagingItems: LazyPagingItems<WorkoutCardItem>,
    mediaPagingItems: LazyPagingItems<WorkoutMediaItem>,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    tabs: Array<Tab>,
    modifier: Modifier = Modifier,
    pagerState: PagerState = rememberPagerState { 2 },
    onMediaIndexSelected: (Int) -> Unit = {},
    nestedScrollConnection: NestedScrollConnection? = null,
    updatedCardList: List<WorkoutCardInfo> = emptyList(),
    workoutSummaryStats: WorkoutSummaryStats? = null,
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
) {
    val density = LocalDensity.current
    val screenHeight = LocalWindowInfo.current.containerSize.height
    val statusBarHeight = WindowInsets.statusBars.getTop(density)
    val navigationBarHeight = WindowInsets.navigationBars.getBottom(density)
    val availableHeight = with(density) {
        (screenHeight - statusBarHeight - TOP_BAR_HEIGHT.toPx() - TAB_HEIGHT.toPx() - navigationBarHeight).toDp()
    }
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(availableHeight)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .fillMaxSize()
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background,
                )
        ) { page ->
            when (tabs[page]) {
                Tab.ACTIVITIES -> WorkoutCardPageContent(
                    pageItems = workoutPagingItems,
                    workoutCardActionsHandler = workoutCardActionsHandler,
                    nestedScrollConnection = nestedScrollConnection,
                    updatedCardList = updatedCardList,
                    workoutSummaryStats = workoutSummaryStats,
                    measurementUnit = measurementUnit,
                )

                Tab.MEDIA -> WorkoutMediaPageContent(
                    pageItems = mediaPagingItems,
                    onMediaIndexSelected = onMediaIndexSelected,
                    nestedScrollConnection = nestedScrollConnection,
                )
            }
        }
    }
}

private enum class Tab(
    @field:StringRes val title: Int,
) {
    ACTIVITIES(R.string.home_activities),
    MEDIA(R.string.media),
}

@Preview
@Composable
private fun TabPreview() {
    M3AppTheme {
        Tab(tabs = Tab.entries.toTypedArray())
    }
}
