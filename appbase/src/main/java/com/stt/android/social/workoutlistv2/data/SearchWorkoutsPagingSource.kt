package com.stt.android.social.workoutlistv2.data

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.stt.android.domain.user.User
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.workoutlistv2.usecase.SearchWorkoutPageUseCase

class SearchWorkoutsPagingSource(
    private val user: User,
    private val query: String,
    private val searchWorkoutsUseCase: SearchWorkoutPageUseCase,
    private val pageSize: Int,
) : PagingSource<Int, WorkoutCardInfo>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, WorkoutCardInfo> {
        val page = params.key ?: 1

        return try {
            val workoutsPage = searchWorkoutsUseCase(
                user = user,
                query = query,
                page = page,
                pageSize = pageSize,
            )

            val nextKey = if (workoutsPage.workouts.isEmpty()) {
                null
            } else {
                page + 1
            }

            LoadResult.Page(
                data = workoutsPage.workouts,
                prevKey = if (page == 1) null else page - 1,
                nextKey = nextKey
            )
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, WorkoutCardInfo>): Int? {
        return state.anchorPosition?.let { position ->
            state.closestPageToPosition(position)?.prevKey?.plus(1)
                ?: state.closestPageToPosition(position)?.nextKey?.minus(1)
        }
    }
}
