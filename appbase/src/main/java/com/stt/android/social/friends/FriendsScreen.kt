package com.stt.android.social.friends

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.widgets.CustomHeightTabIndicator
import com.stt.android.social.friends.discover.DiscoverFriendsContent
import com.stt.android.social.friends.followers.FollowersContent
import com.stt.android.social.friends.following.FollowingContent
import com.stt.android.utils.getLocalizedErrorMessage
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

@Composable
fun FriendsScreen(
    viewModel: FriendsViewModel,
    onFriendClick: (Friend) -> Unit,
    onPhoneContactsClick: () -> Unit,
    onFacebookFriendsClick: () -> Unit,
    onInvitePeopleClick: () -> Unit,
    modifier: Modifier = Modifier,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val friendsTabs = FriendsTab.entries.toTypedArray()
    val currentPage by viewModel.currentPageFlow.collectAsState()
    val pagerState =
        rememberPagerState(initialPage = currentPage) { friendsTabs.size }
    val facebookLoading by viewModel.facebookLoading.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val discoverFriendsState by viewModel.discoverFriendsStateFlow.collectAsState()
    val followingState by viewModel.followingStateFlow.collectAsState()
    val followersState by viewModel.followersStateFlow.collectAsState()
    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }
            .distinctUntilChanged()
            .collect { page ->
                viewModel.updateCurrentPage(page)
            }
    }
    LifecycleStartEffect(key1 = lifecycleOwner) {
        viewModel.loadCurrentPageData(300)
        onStopOrDispose {}
    }

    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.friendsEventFlow.collect { event ->
            val message = when (event) {
                is FriendsError -> event.throwable.getLocalizedErrorMessage(context)
                else -> return@collect
            }
            snackbarHostState.showSnackbar(
                message = message,
                duration = SnackbarDuration.Short,
            )
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        containerColor = MaterialTheme.colorScheme.surface,
        modifier = modifier.fillMaxSize(),
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
        ) {
            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage,
                modifier = Modifier.height(48.dp),
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
                edgePadding = 0.dp,
                divider = {},
                indicator = { tabPositions ->
                    CustomHeightTabIndicator(
                        tabPositions = tabPositions,
                        selectedTabIndex = pagerState.currentPage,
                    )
                }
            ) {
                friendsTabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = stringResource(tab.title),
                                style = MaterialTheme.typography.bodyLargeBold,
                            )
                        },
                        selectedContentColor = MaterialTheme.colorScheme.primary,
                        unselectedContentColor = MaterialTheme.colorScheme.secondary,
                    )
                }
            }

            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    ),
            ) { page ->
                when (friendsTabs[page]) {
                    FriendsTab.DISCOVER -> DiscoverFriendsContent(
                        state = discoverFriendsState,
                        facebookLoading = facebookLoading,
                        onFriendClick = onFriendClick,
                        onStatusClick = viewModel::onStatusClick,
                        onPhoneContactsClick = onPhoneContactsClick,
                        onFacebookFriendsClick = onFacebookFriendsClick,
                        onInvitePeopleClick = onInvitePeopleClick,
                        modifier = Modifier.fillMaxSize(),
                    )

                    FriendsTab.FOLLOWING -> FollowingContent(
                        state = followingState,
                        onFriendClick = onFriendClick,
                        onStatusClick = viewModel::onStatusClick,
                        modifier = Modifier.fillMaxSize(),
                    )

                    FriendsTab.FOLLOWERS -> FollowersContent(
                        state = followersState,
                        onFriendClick = {
                            if (!viewModel.onFriendClick(it)) {
                                onFriendClick(it)
                            }
                        },
                        onStatusClick = viewModel::onStatusClick,
                        onEditClick = { viewModel.setEditingMode(true) },
                        modifier = Modifier.fillMaxSize(),
                        onApproveClick = viewModel::approveFollower,
                        onRejectClick = viewModel::rejectFollower,
                    )
                }
            }
        }
    }
}

enum class FriendsTab(
    @StringRes val title: Int,
) {
    DISCOVER(R.string.find_friends),
    FOLLOWING(R.string.following),
    FOLLOWERS(R.string.followers),
}
