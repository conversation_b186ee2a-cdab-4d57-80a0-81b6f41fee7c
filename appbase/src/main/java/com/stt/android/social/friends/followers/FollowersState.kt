package com.stt.android.social.friends.followers

import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus

data class FollowersState(
    val friends: List<Friend>,
    val editing: <PERSON><PERSON><PERSON>,
    val selectedFriends: List<Friend>,
    val loading: <PERSON><PERSON><PERSON>,
) {
    val requestedFriends = friends.filter { it.friendStatus == FriendStatus.REQUESTED }
    val followers = friends.filter { it.friendStatus != FriendStatus.REQUESTED }
    val followersCount: Int = followers.count()
}
