package com.stt.android.social.badges.badgesDetail

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlin.math.floor

@Composable
fun BadgeActivityIcons(activityIds: ImmutableList<Int>, modifier: Modifier = Modifier) {
    if (!activityIds.isEmpty()) {
        BoxWithConstraints(
            modifier = modifier
                .background(MaterialTheme.colorScheme.surface)
                .padding(
                    horizontal = MaterialTheme.spacing.smaller,
                    vertical = MaterialTheme.spacing.smaller
                )
                .fillMaxWidth()
        ) {
            val imageCount =
                floor((maxWidth / (MaterialTheme.iconSizes.small + MaterialTheme.spacing.xsmall * 2)).toDouble()).toInt()
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = if (activityIds.size > imageCount) Arrangement.SpaceBetween else Arrangement.Start
            ) {
                val shouldShowImageCount =
                    if (activityIds.size > imageCount) imageCount - 1 else imageCount
                activityIds.take(shouldShowImageCount).forEach {
                    SuuntoActivityIcon(
                        it,
                        modifier = Modifier.padding(MaterialTheme.spacing.xsmall),
                        iconSize = MaterialTheme.iconSizes.small
                    )
                }
                if (activityIds.size > imageCount) {
                    Icon(
                        painter = painterResource(com.stt.android.R.drawable.icon_more),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(MaterialTheme.spacing.xsmall)
                            .size(MaterialTheme.iconSizes.small)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun BadgeActivityIconsPreview() {
    M3AppTheme {
        BadgeActivityIcons(
            activityIds = persistentListOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20),
        )
    }
}
