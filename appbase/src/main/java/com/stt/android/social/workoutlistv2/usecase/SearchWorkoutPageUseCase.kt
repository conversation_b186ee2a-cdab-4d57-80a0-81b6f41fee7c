package com.stt.android.social.workoutlistv2.usecase

import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.workout.WorkoutRepository
import com.stt.android.domain.user.User
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.social.workoutlistv2.WorkoutPage
import javax.inject.Inject

class SearchWorkoutPageUseCase @Inject constructor(
    private val workoutRepository: WorkoutRepository,
    private val workoutCardLoader: WorkoutCardLoader,
    private val currentUserController: CurrentUserController,
) {
    suspend operator fun invoke(
        user: User,
        query: String,
        page: Int,
        pageSize: Int,
    ): WorkoutPage {
        val domainWorkouts = workoutRepository.searchWorkouts(
            username = user.username,
            query = query,
            page = page,
            pageSize = pageSize,
        )

        val isOwnWorkout = user.username == currentUserController.username
        val userWorkoutPairs = domainWorkouts.map { user to it }

        val workoutCardInfos = workoutCardLoader.buildWorkoutCardsByDomainWorkouts(
            userWorkoutPairs = userWorkoutPairs,
            isOwnWorkout = isOwnWorkout,
            includeCover = true,
        )

        return WorkoutPage(
            username = user.username,
            page = page,
            workouts = workoutCardInfos
        )
    }
}
