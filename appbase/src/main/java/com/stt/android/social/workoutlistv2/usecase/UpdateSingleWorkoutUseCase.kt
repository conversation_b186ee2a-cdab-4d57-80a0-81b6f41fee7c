package com.stt.android.social.workoutlistv2.usecase

import com.stt.android.controllers.CurrentUserController
import com.stt.android.data.workout.WorkoutRepository
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.AdditionalData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.newfeed.WorkoutCardInfo
import java.util.EnumSet
import javax.inject.Inject

class UpdateSingleWorkoutUseCase @Inject constructor(
    private val workoutRepository: WorkoutRepository,
    private val workoutCardLoader: WorkoutCardLoader,
    private val currentUserController: CurrentUserController,
) {
    suspend operator fun invoke(user: User, workoutHeader: WorkoutHeader): WorkoutCardInfo {
        val key = workoutHeader.key ?: throw IllegalArgumentException("Workout key is null")
        val domainWorkout = workoutRepository.fetchCombinedWorkout(
            username = user.username,
            workoutKey = key,
            extensions = null,
            additionalData = EnumSet.of(
                AdditionalData.PHOTOS,
                AdditionalData.VIDEOS,
                AdditionalData.COMMENTS,
                AdditionalData.USER_REACTED,
                AdditionalData.ACHIEVEMENTS,
            )
        )
        val userWorkoutPairs = listOf(user to domainWorkout)
        val workoutCardInfos = workoutCardLoader.buildWorkoutCardsByDomainWorkouts(
            userWorkoutPairs,
            user.username == currentUserController.currentUser.username,
            true,
        )
        return workoutCardInfos.first()
    }
}
