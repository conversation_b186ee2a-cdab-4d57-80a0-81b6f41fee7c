package com.stt.android.social.friends.others

import android.content.SharedPreferences
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.session.CurrentUser
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.user.follow.FetchOtherUserFollowInfoUseCase
import com.stt.android.domain.user.follow.FollowInfo
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendsError
import com.stt.android.social.friends.FriendsEvent
import com.stt.android.social.friends.FriendsTab
import com.stt.android.social.friends.followers.FollowersState
import com.stt.android.social.friends.following.FollowingState
import com.stt.android.social.friends.others.OthersFriendsActivity.Companion.KEY_SHOW_FOLLOWER
import com.stt.android.social.friends.others.OthersFriendsActivity.Companion.KEY_USERNAME
import com.stt.android.social.friends.usecase.FollowFriendUseCase
import com.stt.android.social.friends.utils.toFriend
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@OptIn(FlowPreview::class)
@HiltViewModel
class OthersFriendsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @FeatureTogglePreferences private val featureTogglePrefs: SharedPreferences,
    private val fetchOtherUserFollowInfoUseCase: FetchOtherUserFollowInfoUseCase,
    private val peopleController: PeopleController,
    private val followFriendUseCase: FollowFriendUseCase,
    private val currentUser: CurrentUser,
    coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {

    private val _currentPageFlow = MutableStateFlow(FriendsTab.DISCOVER.ordinal)
    val currentPageFlow = _currentPageFlow.asStateFlow()

    private val _friendsEventFlow = MutableSharedFlow<FriendsEvent>(
        replay = 0,
        extraBufferCapacity = 1,
    )
    val friendsEventFlow = _friendsEventFlow.asSharedFlow()

    private var _followingStateFlow = MutableStateFlow(FollowingState(emptyList(), true))
    val followingStateFlow = _followingStateFlow.asStateFlow()

    private var _followersStateFlow =
        MutableStateFlow(FollowersState(emptyList(), false, emptyList(), true))
    val followersStateFlow = _followersStateFlow.asStateFlow()

    init {
        val showFollower = savedStateHandle.get<Boolean>(KEY_SHOW_FOLLOWER) ?: false
        val username = savedStateHandle.get<String>(KEY_USERNAME) ?: ""
        _currentPageFlow.update {
            when {
                showFollower -> OthersFriendsTab.FOLLOWERS.ordinal
                else -> OthersFriendsTab.FOLLOWING.ordinal
            }
        }
        viewModelScope.launch(coroutinesDispatchers.io) {
            loadData(username)
        }
    }

    private suspend fun loadData(username: String) {
        val followInfo = runSuspendCatching {
            val needApprove = featureTogglePrefs.getBoolean(
                STTConstants.FeatureTogglePreferences.KEY_ENABLE_APPROVING_FOLLOWERS,
                STTConstants.FeatureTogglePreferences.KEY_ENABLE_APPROVING_FOLLOWERS_DEFAULT
            )
            fetchOtherUserFollowInfoUseCase(username, needApprove)
        }.onFailure {
            sendEvent(FriendsError(it))
            Timber.w(it, "Failed to load other user follow info")
        }.getOrDefault(
            FollowInfo(
                followers = emptyList(),
                followings = emptyList(),
                blocked = emptyList(),
                blockedBy = emptyList(),
            )
        )
        val followingUfsList =
            runSuspendCatching {
                peopleController.loadUfsListFromDbForFollowUser(followInfo.followings)
            }.onFailure {
                sendEvent(FriendsError(it))
                Timber.w(it, "Failed to load other user follow info")
            }.getOrDefault(emptyList())
        val followersUfsList =
            runSuspendCatching {
                peopleController.loadUfsListFromDbForFollowUser(followInfo.followers)
            }.onFailure {
                sendEvent(FriendsError(it))
                Timber.w(it, "Failed to load other user follow info")
            }.getOrDefault(emptyList())
        followingUfsList.map { it.toFriend(currentUser.getUsername()) }.let { friends ->
            _followingStateFlow.update {
                it.copy(
                    friends = friends,
                    loading = false,
                )
            }
        }
        followersUfsList.map { it.toFriend(currentUser.getUsername()) }.let { friends ->
            _followersStateFlow.update {
                it.copy(
                    friends = friends,
                    loading = false,
                )
            }
        }
    }

    fun updateCurrentPage(page: Int) {
        _currentPageFlow.update { page }
    }

    fun onStatusClick(friend: Friend) {
        viewModelScope.launch {
            val updated = runSuspendCatching {
                followFriendUseCase(friend)
            }.onFailure {
                sendEvent(FriendsError(it))
                Timber.w(it, "follow/unfollow ${friend.username} failed.")
            }.getOrNull() ?: return@launch
            _followingStateFlow.update { state ->
                state.copy(
                    friends = state.friends.replaceByUpdated(updated)
                )
            }
            _followersStateFlow.update { state ->
                state.copy(
                    friends = state.friends.replaceByUpdated(updated)
                )
            }
        }
    }

    fun approveFollower(friend: Friend) {
        viewModelScope.launch {
        }
    }

    fun rejectFollower(friend: Friend) {
        viewModelScope.launch {
        }
    }

    private fun sendEvent(event: FriendsEvent) {
        _friendsEventFlow.tryEmit(event)
    }

    private fun List<Friend>.replaceByUpdated(updated: Friend) = map {
        if (it.username == updated.username) {
            updated
        } else {
            it
        }
    }
}
