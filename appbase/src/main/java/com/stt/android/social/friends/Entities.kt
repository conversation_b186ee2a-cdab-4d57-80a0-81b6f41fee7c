package com.stt.android.social.friends

import com.stt.android.follow.FollowDirection

data class Friend(
    val username: String,
    val realName: String,
    val profileDescription: String,
    val profileImageUrl: String,
    val friendStatus: FriendStatus,
    val followDirection: FollowDirection,
) {
    val isValid: Boolean
        get() = username.isNotBlank()
}

enum class FriendStatus {
    REQUESTED,
    FOLLOW,
    FOLLOWING,
    FRIEND,
    ME,
}
