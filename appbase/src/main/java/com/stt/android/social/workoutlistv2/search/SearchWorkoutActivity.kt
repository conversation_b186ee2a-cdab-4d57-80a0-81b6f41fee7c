package com.stt.android.social.workoutlistv2.search

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.ComposeMapSnapshotterBinding
import com.stt.android.maps.MapSnapshotter
import com.stt.android.ui.components.workout.actions.rememberWorkoutCardActionsHandler
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class SearchWorkoutActivity : AppCompatActivity() {

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    private val binding: ComposeMapSnapshotterBinding by lazy {
        ComposeMapSnapshotterBinding.inflate(layoutInflater)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(this@SearchWorkoutActivity)
            }
        }
        setContentView(binding.root)
        binding.composeView.setContentWithM3Theme {
            SearchWorkoutScreen(onBackClicked = ::finish)
        }
    }

    @Composable
    fun SearchWorkoutScreen(
        onBackClicked: () -> Unit,
        modifier: Modifier = Modifier,
        viewModel: SearchWorkoutViewModel = hiltViewModel(),
    ) {
        val viewState by viewModel.viewState.collectAsState()
        val updatedCardList by viewModel.updatedCardFlow.collectAsState()
        val snackbarHostState = remember { SnackbarHostState() }
        val workoutCardActionsHandler = rememberWorkoutCardActionsHandler(
            workoutCardActionsHandler = viewModel.workoutCardActionsHandler,
            fragmentManager = supportFragmentManager,
            snackbarHostState = snackbarHostState,
            analyticsSource = AnalyticsPropertyValue.WorkoutDetailsSourceProperty.PERSONAL_PROFILE_WORKOUT_LIST,
        )

        Scaffold(
            snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
            modifier = modifier,
        ) { paddingValues ->
            SearchWorkoutContent(
                viewState = viewState,
                workoutCardActionsHandler = workoutCardActionsHandler,
                onBackClick = onBackClicked,
                onQueryChange = viewModel::onQueryChange,
                updatedCardList = updatedCardList,
                modifier = Modifier.padding(paddingValues)
            )
        }
    }

    companion object {
        internal const val KEY_USERNAME = "username"

        @JvmStatic
        fun newStartIntent(context: Context, username: String?, trackPageName: String): Intent {
            return Intent(context, SearchWorkoutActivity::class.java)
                .putExtra(KEY_USERNAME, username)
                .putExtra(AnalyticsEventProperty.PAGE_NAME, trackPageName)
        }
    }
}
