package com.stt.android.social.badges.badgesbase

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.social.badges.badgesDetail.BadgeDetailActivity
import com.stt.android.social.badges.badgesList.BadgesListActivity
import com.stt.android.social.badges.myBadgesList.MyBadgesListActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BadgesActivity : ComponentActivity() {

    private val viewModel: BadgesViewModel by viewModels()

    private fun handleBadgesEvent(event: BadgesViewEvent) {
        when (event) {
            is BadgesViewEvent.Close -> {
                finish()
            }

            is BadgesViewEvent.OnListClick -> {
                startActivity(
                    BadgesListActivity.newIntent(this, event.moduleName)
                )
            }

            is BadgesViewEvent.OnMyListClick -> {
                startActivity(
                    MyBadgesListActivity.newIntent(this)
                )
            }

            is BadgesViewEvent.OnBadgesClick -> {
                startActivity(
                    BadgeDetailActivity.newIntent(this, event.badgesId)
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentWithM3Theme {
            val viewData by viewModel.uiState.collectAsState()
            when (viewData) {
                is BadgesViewData.Loaded -> {
                    BadgesLoadedScreen(
                        onEvent = ::handleBadgesEvent,
                        viewData = viewData as BadgesViewData.Loaded,
                        modifier = Modifier
                    )
                }

                is BadgesViewData.Initial -> {
                    Scaffold(
                        topBar = {
                            SuuntoTopBar(
                                title = stringResource(R.string.main_badges_screen_title),
                                onNavigationClick = { finish() },
                            )
                        },
                    ) { paddingValues ->
                        Column(
                            modifier = Modifier
                                .narrowContentWithBgColors(
                                    backgroundColor = MaterialTheme.colorScheme.surface,
                                    outerBackgroundColor = MaterialTheme.colorScheme.background
                                )
                                .padding(paddingValues),
                        ) {
                            LoadingContent(isLoading = true)
                        }
                    }
                }
            }
        }
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, BadgesActivity::class.java)
        }
    }
}
