package com.stt.android.social.badges.myBadgesList

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler

@Composable
internal fun MyBadgesListScreen(
    viewData: MyBadgesListViewData,
    onEvent: (MyBadgesListViewEvent) -> Unit,
) {
    Scaffold(
        topBar = {
            MyBadgesListTopBar(
                title = R.string.my_badges_title,
                onEvent = onEvent
            )
        }
    ) { paddingValues ->
        when (viewData) {
            is MyBadgesListViewData.Initial -> {
                Box(modifier = Modifier.fillMaxSize()) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(48.dp),
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            }

            is MyBadgesListViewData.Loaded -> {
                if (viewData.myBadgesList.isNotEmpty()) {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(paddingValues)
                            .narrowContentWithBgColors(
                                backgroundColor = MaterialTheme.colorScheme.surface,
                                outerBackgroundColor = MaterialTheme.colorScheme.background
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        viewData.myBadgesList.forEach { (moduleName, badges) ->
                            item {
                                Text(
                                    text = moduleName,
                                    style = MaterialTheme.typography.bodyLargeBold,
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .padding(
                                            start = MaterialTheme.spacing.medium,
                                            top = MaterialTheme.spacing.large,
                                            bottom = MaterialTheme.spacing.small
                                        )
                                )
                            }
                            item {
                                BadgesGrid(
                                    badges = badges,
                                    onEvent = onEvent,
                                )
                            }
                        }
                    }
                } else {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .narrowContentWithBgColors(
                                backgroundColor = MaterialTheme.colorScheme.surface,
                                outerBackgroundColor = MaterialTheme.colorScheme.background
                            ),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Image(
                            painter = painterResource(R.drawable.empty_my_badges_list),
                            contentDescription = "recent badge",
                            modifier = Modifier
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun BadgesGrid(
    badges: List<MyBadgesDisplayItem>,
    onEvent: (MyBadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = modifier
            .fillMaxSize()
            .padding(MaterialTheme.spacing.medium)
            .heightIn(min = 120.dp, max = 30000.dp),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        userScrollEnabled = true
    ) {
        items(badges) { badge ->
            BadgeGridItem(badge = badge, onEvent = onEvent)
        }
    }
}

@Composable
private fun BadgeGridItem(
    badge: MyBadgesDisplayItem,
    onEvent: (MyBadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier,
    throttleClicks: Boolean = false,
) {
    val eventThrottler = if (throttleClicks) rememberEventThrottler() else null
    val imageUrl = badge.acquiredBadgeIconUrl ?: badge.badgeIconUrl
    Column(
        modifier = modifier
            .padding(MaterialTheme.spacing.xsmall)
            .clickable {
                eventThrottler?.processEvent {
                    onEvent(
                        MyBadgesListViewEvent.OnMyListBadgesClick(
                            badge.badgeConfigId
                        )
                    )
                } ?: onEvent(MyBadgesListViewEvent.OnMyListBadgesClick(badge.badgeConfigId))
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AsyncImage(
            model = imageUrl,
            contentDescription = badge.badgeName,
            modifier = Modifier
                .size(80.dp)
                .alpha(if (badge.isAcquired) 1f else 0.5f)
        )
        Text(
            text = badge.badgeName ?: "",
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .width(100.dp)
                .padding(MaterialTheme.spacing.xsmall)
        )
    }
}

@Composable
private fun MyBadgesListTopBar(
    @StringRes title: Int,
    onEvent: (MyBadgesListViewEvent) -> Unit,
) {
    SuuntoTopBar(
        title = stringResource(id = title),
        onNavigationClick = { onEvent(MyBadgesListViewEvent.Close) },
    )
}
