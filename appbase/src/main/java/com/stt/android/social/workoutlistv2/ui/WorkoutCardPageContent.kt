package com.stt.android.social.workoutlistv2.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyXLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.social.workoutlist.ui.WorkoutStats
import com.stt.android.social.workoutlistv2.WorkoutCardItem
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.utils.isPositive
import timber.log.Timber

@Composable
fun WorkoutCardPageContent(
    pageItems: LazyPagingItems<WorkoutCardItem>,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    modifier: Modifier = Modifier,
    nestedScrollConnection: NestedScrollConnection? = null,
    updatedCardList: List<WorkoutCardInfo> = emptyList(),
    workoutSummaryStats: WorkoutSummaryStats? = null,
    measurementUnit: MeasurementUnit = MeasurementUnit.METRIC,
) {
    val refreshState = pageItems.loadState.refresh
    val appendState = pageItems.loadState.append
    val nestedScrollModifier = nestedScrollConnection?.let { Modifier.nestedScroll(it) } ?: Modifier
    val isRefreshing = refreshState is LoadState.Loading
    val isError = refreshState is LoadState.Error
    val isEmpty = refreshState is LoadState.NotLoading && pageItems.itemCount == 0
    val isFullyLoaded = appendState.endOfPaginationReached && pageItems.itemCount > 0
    val isNotAllShown =
        isFullyLoaded && pageItems.itemCount < (workoutSummaryStats?.totalNumberOfWorkoutsSum ?: 0)
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .narrowContent()
            .then(nestedScrollModifier),
    ) {
        if (workoutSummaryStats != null) {
            item(key = "workout_stats") {
                WorkoutStats(
                    unit = measurementUnit,
                    workoutSummaryStats = workoutSummaryStats,
                )
            }
        }

        when {
            isRefreshing -> {
                item {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = MaterialTheme.spacing.large),
                    ) {
                        CircularProgressIndicator()
                    }
                }
            }

            isEmpty -> {
                item {
                    val hasSummary = workoutSummaryStats?.totalNumberOfWorkoutsSum.isPositive()
                    EmptyView(
                        icon = R.drawable.ic_svg_no_activity,
                        tips = if (hasSummary) {
                            R.string.only_show_public_activities
                        } else {
                            R.string.no_activities_no_tracking
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = MaterialTheme.spacing.large),
                    )
                }
            }

            isError -> {
                item {
                    ErrorView(
                        error = refreshState.error,
                        onRetryClicked = {
                            pageItems.retry()
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = MaterialTheme.spacing.large),
                    )
                }
            }

            else -> {
                val snapshotList = pageItems.itemSnapshotList
                val dateHeaderIndices =
                    snapshotList.indices.filter { snapshotList[it] is WorkoutCardItem.Header }
                if (dateHeaderIndices.any()) {
                    withDateHeaderContent(
                        dateHeaderIndices,
                        pageItems,
                        workoutCardActionsHandler,
                        updatedCardList
                    )
                } else {
                    withoutDateHeaderContent(pageItems, workoutCardActionsHandler, updatedCardList)
                }

                when {
                    appendState is LoadState.Loading -> {
                        item {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = MaterialTheme.spacing.medium),
                            ) {
                                CircularProgressIndicator(
                                    strokeWidth = 2.dp,
                                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                                )
                            }
                        }
                    }

                    appendState is LoadState.Error -> {
                        item {
                            ErrorView(
                                error = appendState.error,
                                onRetryClicked = {
                                    pageItems.retry()
                                },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = MaterialTheme.spacing.medium),
                            )
                        }
                    }

                    isNotAllShown -> {
                        item {
                            Text(
                                text = stringResource(R.string.only_show_public_activities),
                                color = MaterialTheme.colorScheme.secondary,
                                textAlign = TextAlign.Center,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = MaterialTheme.spacing.medium),
                            )
                        }
                    }
                }
            }
        }
    }
}

private fun LazyListScope.withDateHeaderContent(
    dateHeaderIndices: List<Int>,
    pageItems: LazyPagingItems<WorkoutCardItem>,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    updatedCardList: List<WorkoutCardInfo>,
) {
    dateHeaderIndices.forEachIndexed { i, headerIndex ->
        val nextHeaderIndex = dateHeaderIndices.getOrNull(i + 1) ?: pageItems.itemCount
        val dateHeader = pageItems[headerIndex] as? WorkoutCardItem.Header ?: return@forEachIndexed
        val count = nextHeaderIndex - headerIndex - 1
        stickyHeader(key = "sticky_${dateHeader.yearMonth}") {
            DateHeader(
                yearMonth = dateHeader.yearMonth,
                count = count,
                modifier = Modifier.background(MaterialTheme.colorScheme.surface),
            )
        }

        val startIndex = headerIndex + 1
        val endIndex = nextHeaderIndex

        items(
            count = endIndex - startIndex,
            key = { index ->
                val item = pageItems.peek(startIndex + index) as? WorkoutCardItem.Workout
                item?.data?.workoutHeader?.key ?: (startIndex + index)
            }
        ) { indexInSection ->
            val itemInPage = pageItems[startIndex + indexInSection]
            if (itemInPage is WorkoutCardItem.Workout) {
                val workoutCardInfo =
                    updatedCardList.firstOrNull { it.workoutHeader.key == itemInPage.data.workoutHeader.key }
                        ?: itemInPage.data
                WorkoutCard(
                    viewData = workoutCardInfo.workoutCardViewData,
                    onClick = { workoutCardActionsHandler.onOpenWorkout(workoutCardInfo.workoutHeader) },
                    onEditClick = { workoutCardActionsHandler.onEditWorkout(workoutCardInfo.workoutHeader) },
                    onAddCommentClick = { workoutCardActionsHandler.onAddComment(workoutCardInfo.workoutHeader) },
                    onReactionClick = {
                        workoutCardActionsHandler.onReaction(workoutCardInfo.workoutHeader)
                    },
                    onShareClick = { shareInfo ->
                        workoutCardActionsHandler.onShareWorkout(
                            workoutCardInfo.workoutHeader,
                            shareInfo
                        )
                    },
                    onTagClicked = { tagName, isEditable ->
                        workoutCardActionsHandler.onTagClicked(tagName, isEditable)
                    },
                    onAddPhotoClick = { workoutCardActionsHandler.onAddPhoto(workoutCardInfo.workoutHeader) },
                    onPlayClick = { workoutCardActionsHandler.onPlayWorkout(workoutCardInfo.workoutHeader) },
                    onUserClick = { username -> workoutCardActionsHandler.onUserClick(username) },
                    modifier = Modifier.padding(
                        vertical = MaterialTheme.spacing.small,
                        horizontal = MaterialTheme.spacing.medium,
                    ),
                )
            }
        }
    }
}

private fun LazyListScope.withoutDateHeaderContent(
    pageItems: LazyPagingItems<WorkoutCardItem>,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    updatedCardList: List<WorkoutCardInfo>,
) {
    items(pageItems.itemCount, key = { index ->
        (pageItems.peek(index) as? WorkoutCardItem.Workout)
            ?.data?.workoutHeader?.key ?: "placeholder_$index"
    }) { index ->
        val item = pageItems[index] as? WorkoutCardItem.Workout ?: return@items
        val workoutCardInfo =
            updatedCardList.firstOrNull { it.workoutHeader.key == item.data.workoutHeader.key }
                .also {
                    Timber.d("Updated card: $it")
                } ?: item.data
        val workoutHeader = workoutCardInfo.workoutHeader
        WorkoutCard(
            viewData = workoutCardInfo.workoutCardViewData,
            onClick = { workoutCardActionsHandler.onOpenWorkout(workoutHeader) },
            onEditClick = { workoutCardActionsHandler.onEditWorkout(workoutHeader) },
            onAddCommentClick = { workoutCardActionsHandler.onAddComment(workoutHeader) },
            onReactionClick = { workoutCardActionsHandler.onReaction(workoutHeader) },
            onShareClick = { shareInfo ->
                workoutCardActionsHandler.onShareWorkout(
                    workoutHeader,
                    shareInfo
                )
            },
            onTagClicked = { tagName, isEditable ->
                workoutCardActionsHandler.onTagClicked(
                    tagName,
                    isEditable
                )
            },
            onAddPhotoClick = { workoutCardActionsHandler.onAddPhoto(workoutHeader) },
            onPlayClick = { workoutCardActionsHandler.onPlayWorkout(workoutHeader) },
            onUserClick = { username -> workoutCardActionsHandler.onUserClick(username) },
            modifier = Modifier.padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
        )
    }
}

@Composable
private fun DateHeader(
    yearMonth: String,
    count: Int,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(
                vertical = MaterialTheme.spacing.small,
                horizontal = MaterialTheme.spacing.medium,
            ),
    ) {
        Text(
            text = yearMonth,
            style = MaterialTheme.typography.body,
        )
        Text(
            text = pluralStringResource(
                R.plurals.workouts_plural,
                count,
                count,
            ),
            style = MaterialTheme.typography.body,
        )
    }
}

@Composable
private fun ErrorView(
    onRetryClicked: () -> Unit,
    modifier: Modifier = Modifier,
    error: Throwable? = null,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        modifier = modifier,
    ) {
        Text(
            text = stringResource(R.string.error_0),
            style = MaterialTheme.typography.bodyXLarge,
        )

        TextButton(
            onClick = onRetryClicked,
        ) {
            Text(
                text = stringResource(R.string.retry),
            )
        }
    }
}

@Preview
@Composable
private fun WorkoutCardPageContentPreview() {
    M3AppTheme {
        ErrorView({})
    }
}
