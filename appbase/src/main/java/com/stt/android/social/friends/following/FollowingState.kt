package com.stt.android.social.friends.following

import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus

data class FollowingState(
    val friends: List<Friend>,
    val loading: <PERSON><PERSON><PERSON>,
) {
    val followingCount: Int = friends.count {
        it.friendStatus != FriendStatus.FOLLOW && it.friendStatus != FriendStatus.REQUESTED
    }

    val requestedFriends = friends.filter { it.friendStatus == FriendStatus.REQUESTED }
    val following = friends.filter { it.friendStatus != FriendStatus.REQUESTED }
}
