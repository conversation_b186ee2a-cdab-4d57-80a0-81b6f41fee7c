package com.stt.android.social.workoutlistv2.ui

import android.view.LayoutInflater
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.paging.compose.LazyPagingItems
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.newdesign.widgets.PrimaryButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.spacing
import com.stt.android.databinding.ViewExoPlayerViewBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.workoutlistv2.WorkoutMediaItem
import com.stt.android.utils.rememberExoPlayer

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun WorkoutMediaViewerScreen(
    pageItems: LazyPagingItems<WorkoutMediaItem>,
    initialPostIndex: Int,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    onCheckActivities: (WorkoutHeader) -> Unit = {}
) {
    if (pageItems.itemCount == 0) return

    BackHandler { onBackClick() }

    val pagerState = rememberPagerState(
        initialPage = initialPostIndex
    ) { pageItems.itemCount }

    LaunchedEffect(pagerState.currentPage) {
        val nextPageIndex = pagerState.currentPage + 1
        if (nextPageIndex < pageItems.itemCount) {
            pageItems[nextPageIndex]
        }
    }

    LaunchedEffect(pageItems.itemCount) {
        pagerState.scrollToPage(
            pagerState.currentPage.coerceIn(0 until pageItems.itemCount)
        )
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
        ) { page ->
            val post = pageItems[page]
            when (post) {
                is WorkoutMediaItem.Photo -> {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(post.uri)
                            .crossfade(true)
                            .build(),
                        contentDescription = null,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Fit,
                    )
                }
                is WorkoutMediaItem.Video -> {
                    Video(
                        video = post,
                        modifier = Modifier.fillMaxSize(),
                    )
                }
                null -> {
                    Box(
                        Modifier
                            .fillMaxSize()
                            .background(Color.Black),
                        contentAlignment = Alignment.Center,
                    ) {
                        CircularProgressIndicator(color = Color.White)
                    }
                }
            }
        }

        CenterAlignedTopAppBar(
            title = {
                Text("${pagerState.currentPage + 1}/${pageItems.itemCount}")
            },
            navigationIcon = {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClick,
                    contentDescription = stringResource(R.string.back),
                )
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = Color.Transparent,
                titleContentColor = MaterialTheme.colorScheme.surface,
                navigationIconContentColor = MaterialTheme.colorScheme.surface,
            ),
            modifier = Modifier.statusBarsPadding()
        )

        val currentItem = pageItems[pagerState.currentPage]
        if (currentItem?.workoutHeader?.key != null) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.Transparent)
                    .padding(MaterialTheme.spacing.medium)
                    .navigationBarsPadding()
                    .align(Alignment.BottomCenter)
            ) {
                PrimaryButton(
                    text = stringResource(R.string.check_activities).uppercase(),
                    onClick = { onCheckActivities(currentItem.workoutHeader) },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

@Composable
private fun Video(
    video: WorkoutMediaItem.Video,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val player = rememberExoPlayer(context, lifecycleOwner, video.userAgent, video.uri)
    AndroidView(
        factory = {
            ViewExoPlayerViewBinding.inflate(LayoutInflater.from(context))
                .root
                .apply {
                    resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FIT
                    useController = false
                    this.player = player
                }
        },
        modifier = modifier,
    )
}
