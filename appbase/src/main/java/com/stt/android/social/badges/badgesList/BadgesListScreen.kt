package com.stt.android.social.badges.badgesList

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler
import com.stt.android.core.utils.EventThrottler
import com.stt.android.social.badges.badgesbase.BadgesViewEvent

@Composable
internal fun BadgesListScreen(
    viewData: BadgesListViewData,
    onEvent: (BadgesListViewEvent) -> Unit,
) {
    Scaffold(
        topBar = {
            BadgesListTopBar(
                title = (viewData as? BadgesListViewData.Loaded)?.moduleName ?: "",
                onEvent = onEvent,
            )
        },
        containerColor = MaterialTheme.colorScheme.surface,
    ) { paddingValues ->
        Box(modifier = Modifier.padding(paddingValues)) {
            when (viewData) {
                is BadgesListViewData.Initial -> {
                    Box(modifier = Modifier.fillMaxSize()) {
                        CircularProgressIndicator(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .size(48.dp),
                            color = MaterialTheme.colorScheme.primary,
                        )
                    }
                }

                is BadgesListViewData.Loaded -> {
                    Box(modifier = Modifier.fillMaxSize()) {
                        BadgesListContent(badges = viewData.badgesList, onEvent = onEvent)
                    }
                }
            }
        }
    }
}

@Composable
internal fun BadgesListTopBar(
    title: String,
    onEvent: (BadgesListViewEvent) -> Unit,
) {
    SuuntoTopBar(
        title = title,
        onNavigationClick = { onEvent(BadgesListViewEvent.Close) },
    )
}

@Composable
private fun BadgesListContent(
    badges: List<BadgesDisplayItem>,
    onEvent: (BadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    val eventThrottler = rememberEventThrottler()
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = modifier
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background
            ),
        contentPadding = PaddingValues(MaterialTheme.spacing.medium),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large)
    ) {
        items(badges) { badge ->
            BadgeItem(
                badge = badge,
                eventThrottler = eventThrottler,
                onEvent = onEvent
            )
        }
    }
}

// TODO: Implement top activity badges display
@Composable
private fun TopActivityListBadges(
    badges: Map<String, List<BadgesDisplayItem>>,
    onEvent: (BadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(modifier = modifier) {
        badges.forEach { (title, badges) ->
            stickyHeader {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    fontWeight = FontWeight.Bold
                )
            }
            items(badges) { badge ->
                BadgeItem(badge = badge, onEvent = onEvent)
            }
        }
    }
}

@Composable
private fun BadgeItem(
    badge: BadgesDisplayItem,
    onEvent: (BadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier,
    eventThrottler: EventThrottler? = null,
) {
    val imageUrl = if (badge.isAcquired) {
        badge.acquiredBadgeIconUrl
    } else {
        badge.badgeIconUrl
    }

    Column(
        modifier = modifier
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() },
                onClick = {
                    eventThrottler?.processEvent {
                        onEvent(
                            BadgesListViewEvent.OnListBadgesClick(
                                badge.badgeConfigId
                            )
                        )
                    } ?: onEvent(BadgesListViewEvent.OnListBadgesClick(badge.badgeConfigId))
                }
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(imageUrl)
                .crossfade(true)
                .build(),
            contentDescription = badge.badgeName,
            modifier = Modifier.size(80.dp),
            contentScale = ContentScale.Crop
        )
        badge.badgeName?.let {
            Text(
                text = it,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
        }
    }
}
