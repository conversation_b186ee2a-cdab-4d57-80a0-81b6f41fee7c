package com.stt.android.social.friends.others

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.widgets.CustomHeightTabIndicator
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendsError
import com.stt.android.social.friends.followers.FollowersContent
import com.stt.android.social.friends.following.FollowingContent
import com.stt.android.utils.getLocalizedErrorMessage
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OthersFriendsScreen(
    onBackClick: () -> Unit,
    onFriendClick: (Friend) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: OthersFriendsViewModel = hiltViewModel(),
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val friendsTabs = OthersFriendsTab.entries.toTypedArray()
    val currentPage by viewModel.currentPageFlow.collectAsState()
    val pagerState =
        rememberPagerState(initialPage = currentPage) { friendsTabs.size }
    val coroutineScope = rememberCoroutineScope()
    val followingState by viewModel.followingStateFlow.collectAsState()
    val followersState by viewModel.followersStateFlow.collectAsState()
    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }
            .distinctUntilChanged()
            .collect { page ->
                viewModel.updateCurrentPage(page)
            }
    }

    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.friendsEventFlow.collect { event ->
            val message = when (event) {
                is FriendsError -> event.throwable.getLocalizedErrorMessage(context)
                else -> return@collect
            }
            snackbarHostState.showSnackbar(
                message = message,
                duration = SnackbarDuration.Short,
            )
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(stringResource(R.string.friends).uppercase())
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = onBackClick,
                        contentDescription = stringResource(R.string.back),
                    )
                }
            )
        },
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        containerColor = MaterialTheme.colorScheme.surface,
        modifier = modifier.fillMaxSize(),
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
        ) {
            ScrollableTabRow(
                selectedTabIndex = pagerState.currentPage,
                modifier = Modifier.height(48.dp),
                containerColor = Color.Transparent,
                contentColor = MaterialTheme.colorScheme.primary,
                edgePadding = 0.dp,
                divider = {},
                indicator = { tabPositions ->
                    CustomHeightTabIndicator(
                        tabPositions = tabPositions,
                        selectedTabIndex = pagerState.currentPage,
                    )
                }
            ) {
                friendsTabs.forEachIndexed { index, tab ->
                    Tab(
                        selected = pagerState.currentPage == index,
                        onClick = {
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = {
                            Text(
                                text = stringResource(tab.title),
                                style = MaterialTheme.typography.bodyLargeBold,
                            )
                        },
                        selectedContentColor = MaterialTheme.colorScheme.primary,
                        unselectedContentColor = MaterialTheme.colorScheme.secondary,
                    )
                }
            }

            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxSize()
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    ),
            ) { page ->
                when (friendsTabs[page]) {
                    OthersFriendsTab.FOLLOWING -> FollowingContent(
                        state = followingState,
                        onFriendClick = onFriendClick,
                        onStatusClick = viewModel::onStatusClick,
                        modifier = Modifier.fillMaxSize(),
                        isOthers = true,
                    )

                    OthersFriendsTab.FOLLOWERS -> FollowersContent(
                        state = followersState,
                        onFriendClick = onFriendClick,
                        onStatusClick = viewModel::onStatusClick,
                        onEditClick = {},
                        modifier = Modifier.fillMaxSize(),
                        isOthers = true,
                        onApproveClick = viewModel::approveFollower,
                        onRejectClick = viewModel::rejectFollower,
                    )
                }
            }
        }
    }
}

enum class OthersFriendsTab(
    @StringRes val title: Int,
) {
    FOLLOWING(R.string.following),
    FOLLOWERS(R.string.followers),
}
