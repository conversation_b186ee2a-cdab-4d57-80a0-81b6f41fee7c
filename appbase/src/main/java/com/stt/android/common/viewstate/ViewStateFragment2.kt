package com.stt.android.common.viewstate

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.annotation.LayoutRes
import androidx.databinding.ViewDataBinding
import androidx.databinding.library.baseAdapters.BR
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.DataBindingHolder
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.observeNotNull
import timber.log.Timber

/**
 * A base Fragment class that implements the ViewState pattern using [LoadingStateViewModel].
 *
 * It observes [LoadingStateViewModel.viewState] and dispatches relevant events. You need to implement [onStateChanged]
 * to be notified of the state event changes. Note that error events are handled and shown in a [Snackbar]
 * depending on the passed [ErrorEvent].
 *
 * The extending class must use Hilt for injection and provide the [viewModel] and [layoutId].
 *
 * @param ViewStateData The data type that will be wrapped inside [ViewState] which
 * is emitted by all the _notify_ methods in [LoadingStateViewModel] such as [LoadingStateViewModel.notifyDataLoaded]
 * @param ViewModel The type of view model
 *
 * @see [LoadingStateViewModel]
 * @see [ViewState]
 */
abstract class ViewStateFragment2<
    ViewStateData,
    ViewModel : LoadingStateViewModel<ViewStateData>,
    > : Fragment() {

    /**
     * The [ViewModel] that is associated to this Fragment
     */
    abstract val viewModel: ViewModel

    @get:LayoutRes
    abstract val layoutId: Int

    private var snackbar: Snackbar? = null

    private val bindingHolder: DataBindingHolder = DataBindingHolder()

    private val binding get() = bindingHolder.binding!!

    @Suppress("UNCHECKED_CAST")
    protected fun <T : ViewDataBinding> requireBinding() = if (isBindingAvailable) {
        bindingHolder.binding as T
    } else {
        throw IllegalStateException("Binding not available")
    }

    val isBindingAvailable: Boolean
        get() = bindingHolder.binding != null

    @CallSuper
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        bindingHolder.inflate(
            inflater,
            layoutId,
            container,
            false
        ).apply {
            lifecycleOwner = viewLifecycleOwner
            setVariable(BR.viewModel, viewModel)
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.viewState.observeNotNull(viewLifecycleOwner) { state ->
            if (state is ViewState.Error) {
                showErrorSnackbar(state.errorEvent)
            } else {
                snackbar?.dismiss()
                snackbar = null
            }
            onStateChanged(state)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        bindingHolder.clear()
    }

    /**
     * Default empty implementation, override it when needed in your subclass
     */
    abstract fun onStateChanged(state: ViewState<ViewStateData?>)

    private fun showErrorSnackbar(errorEvent: ErrorEvent) {
        val binding = this.binding
        snackbar = Snackbar.make(
            binding.root,
            errorEvent.errorStringRes,
            if (errorEvent.showCloseButton || errorEvent.canRetry) {
                Snackbar.LENGTH_INDEFINITE
            } else {
                Snackbar.LENGTH_LONG
            }
        )
        if (errorEvent.canRetry) {
            snackbar?.setAction(R.string.retry_action) {
                Timber.d("Retry button clicked")
                viewModel.retryLoading()
            }
        } else {
            if (errorEvent.showCloseButton) {
                snackbar?.setAction(R.string.close) {}
            }
        }
        snackbar?.show()
    }
}
