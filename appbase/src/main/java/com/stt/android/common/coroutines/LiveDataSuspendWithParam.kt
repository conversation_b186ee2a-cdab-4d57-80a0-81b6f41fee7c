package com.stt.android.common.coroutines

import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

/**
 * Wrap a suspend function ([action]) that produces a result as a LiveData. The suspend function
 * may either produce a value or throw.
 *
 * Use [run] to start execution. Execution will be cancelled when [cancel] is called or
 * [coroutineScope] is cancelled.
 *
 * Calling [run] a second time cancels the previous operation if it was still ongoing.
 *
 * The wrapper has an observable [state].
 * - Initially the state is [State.Idle]
 * - After the execution is initiated by calling [run], the state changes to [State.InProgress]
 * - If [action] completes and returns a value, the state will be [State.Success] and the value
 *   is available.
 * - If [action] throws, state will be [State.Failure] and the [Throwable] will be available
 */
open class LiveDataSuspendWithParam<Result : Any, Params : Any?>(
    private val coroutineScope: Lazy<CoroutineScope>,
    private val coroutineContext: CoroutineContext = EmptyCoroutineContext,
    private val action: suspend CoroutineScope.(Params) -> Result
) {

    val state: LiveData<LiveDataSuspendState<Result>>
        get() = _state

    @MainThread
    fun run(params: Params) {
        val oldJob = job
        job?.cancel()
        job = coroutineScope.value.launch(coroutineContext) {
            // Wait for previous job to finish to make sure it won't call postValue() from another
            // thread
            oldJob?.join()

            _state.postValue(LiveDataSuspendState.InProgress())
            val result = runCatching { action(params) }
            when {
                result.isSuccess -> {
                    result.getOrNull()?.run {
                        Timber.d("Success: value=$this")
                        _state.postValue(LiveDataSuspendState.Success(this))
                    }
                }
                result.isFailure -> {
                    val exception = result.exceptionOrNull()
                    if (exception is CancellationException || exception == null) {
                        _state.postValue(LiveDataSuspendState.Idle())
                    } else {
                        Timber.d(exception, "Wrapped operation failed")
                        _state.postValue(LiveDataSuspendState.Failure(exception))
                    }
                }
            }
        }
    }

    @MainThread
    fun cancel() {
        job?.cancel()
        job = null
        _state.value = LiveDataSuspendState.Idle()
    }

    private var job: Job? = null
    private val _state = MutableLiveData<LiveDataSuspendState<Result>>(
        LiveDataSuspendState.Idle()
    )
}
