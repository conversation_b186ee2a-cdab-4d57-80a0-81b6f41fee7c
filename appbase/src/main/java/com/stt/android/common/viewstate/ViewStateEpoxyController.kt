package com.stt.android.common.viewstate

import android.os.Handler
import com.airbnb.epoxy.EpoxyAsyncUtil
import com.airbnb.epoxy.Typed2EpoxyController
import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.loading

/**
 * Base epoxy controller that takes an instance of [ViewState<T?>]. This class takes care of building the
 * Epoxy models a.k.a. list items. Override [buildModels] and optionally call the super method at the end to show a
 * loading progress bar when the data is loading. The order you add the models will be the order the items would
 * be shown in the RecyclerView. Learn more about Epoxy controllers
 * [here](https://github.com/airbnb/epoxy/wiki/Epoxy-Controller#usage-with-kotlin).
 *
 * The loading spinner will adapt itself based on the status of the data passed to [setData].
 * When the [ViewState.data] is null, it will automatically stretch itself to fit the size of
 * the RecyclerView - normally this would occupy the whole screen. On the other hand, when there's already
 * existing data, such as when loading more data, the loading spinner will be displayed as a list item.
 *
 * Epoxy relies heavily on Data Binding. To add new model types, you need to create a layout XML
 * filename starting with `viewholder_`. **The layout must be Data Binding enabled**.
 *
 * Note that model building and diffing is automatically done on a background thread.
 * When extending this class you'd normally won't need to change the default handlers.
 * This option is given purely for unit testing.
 *
 * @param modelBuildingHandler The [Handler] to use for building epoxy models
 * @param diffingHandler The [Handler] to use for diffing epoxy models
 * @param T The data type that will be wrapped inside [ViewState]. Must be nullable.
 *
 * @see [ViewState]
 */
abstract class ViewStateEpoxyController<T>(
    modelBuildingHandler: Handler = EpoxyAsyncUtil.getAsyncBackgroundHandler(),
    diffingHandler: Handler = EpoxyAsyncUtil.getAsyncBackgroundHandler()
) : TypedEpoxyController<ViewState<T?>>(modelBuildingHandler, diffingHandler) {

    /**
     * Builds the epoxy models and adds a loading model at the end.
     *
     * When extending this class you **must** override this method to build the
     * epoxy models. You'd normally want to call the super method at the end
     * so that a loading progress bar would be shown when there's no content
     * or additional content is loading.
     */
    override fun buildModels(viewState: ViewState<T?>) {
        if (viewState.isLoading()) {
            loading {
                id("loading")
                fitParent(viewState.data == null)
            }
        }
    }
}

/**
 * Similar to [ViewStateEpoxyController], but takes two separate ViewStates to build the View.
 * The ViewState [T] is considered the primary ViewState that controls the loading spinner's
 * visibility and positioning. [buildModels] can still be called with only [U] loaded,
 * so if the secondary content from [U] shouldn't be shown without contents from [T] its up
 * to the implementing class to handle that.
 *
 * @param T A data type that will be wrapped inside [ViewState]. Must be nullable.
 * @param U A data type that will be wrapped inside [ViewState]. Must be nullable.
 *
 * @param modelBuildingHandler The [Handler] to use for building epoxy models
 * @param diffingHandler The [Handler] to use for diffing epoxy models
 *
 * @see [ViewStateEpoxyController]
 */
abstract class ViewState2EpoxyController<T, U>(
    modelBuildingHandler: Handler = EpoxyAsyncUtil.getAsyncBackgroundHandler(),
    diffingHandler: Handler = EpoxyAsyncUtil.getAsyncBackgroundHandler()
) : Typed2EpoxyController<ViewState<T?>, ViewState<U?>>(modelBuildingHandler, diffingHandler) {

    override fun buildModels(viewState: ViewState<T?>, secondaryViewState: ViewState<U?>) {
        if (viewState.isLoading()) {
            loading {
                id("loading")
                fitParent(viewState.data == null)
            }
        }
    }
}
