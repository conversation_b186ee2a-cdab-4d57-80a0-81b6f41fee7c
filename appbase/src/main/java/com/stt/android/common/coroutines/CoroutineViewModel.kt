package com.stt.android.common.coroutines

import androidx.lifecycle.ViewModel
import com.stt.android.coroutines.LoggingExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel

/**
 * Creates a parent [CoroutineScope] that executes coroutines on the main thread with an exception handler
 * that captures all exceptions on non-main thread. See [LoggingExceptionHandler] for more info.
 */
private fun createParentScope() = CoroutineScope(Dispatchers.Main + SupervisorJob() + LoggingExceptionHandler)

/**
 * A ViewModel that supports launching coroutines
 *
 * Call `launch` to execute coroutines. Note that the coroutines are launched
 * on the main dispatcher (Main Thread) by default so make sure that all the suspend functions you call
 * are Main Safe, meaning that if the function does I/O operation or long computation it should switch dispatcher
 * internally by using `withContext(dispatcher)`. For example:
 *
 * ```
 * suspend fun getSleepUseCase() = withContext(Dispatchers.IO) {
 *   ...
 * }
 * ```
 *
 * Now `getSleepUseCase()` is considered Main Safe and can be called like so:
 *
 *
 * ```
 * fun loadData() = launch {
 *      getSleepUseCase()
 *      sleepLiveData.value = sleepData
 * }
 *
 * ```
 *
 * @param dispatchers an implementation of [CoroutinesDispatchers] that provides the concrete
 * coroutine dispatchers to be used
 *
 * @see [CoroutinesDispatchers]
 */
abstract class CoroutineViewModel(
    dispatchers: CoroutinesDispatchers
) : ViewModel(),
    CoroutinesDispatchers by dispatchers,
    CoroutineScope by createParentScope() {
    override fun onCleared() {
        super.onCleared()
        coroutineContext.cancel()
    }
}
