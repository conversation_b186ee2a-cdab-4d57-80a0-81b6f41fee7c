package com.stt.android.common.coroutines

sealed class LiveDataSuspendState<T>(val value: T?) : HandleableState() {
    class Idle<T> : LiveDataSuspendState<T>(null)
    class InProgress<T> : LiveDataSuspendState<T>(null)
    data class Success<T>(val successValue: T) : LiveDataSuspendState<T>(successValue)
    data class Failure<T>(val throwable: Throwable) : LiveDataSuspendState<T>(null)

    val idle: Boolean
        get() = this is Idle

    val isSuccess: Boolean
        get() = this is Success

    val isFailure: <PERSON>olean
        get() = this is Failure

    val isInProgress: Boolean
        get() = this is InProgress
}
