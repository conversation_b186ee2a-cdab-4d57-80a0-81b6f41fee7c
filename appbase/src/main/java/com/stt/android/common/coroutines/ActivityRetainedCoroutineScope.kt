package com.stt.android.common.coroutines

import dagger.hilt.android.ActivityRetainedLifecycle
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

@ActivityRetainedScoped
class ActivityRetainedCoroutineScope @Inject constructor(
    dispatchers: CoroutinesDispatchers,
    activityRetainedLifecycle: ActivityRetainedLifecycle
) : CoroutineScope {
    override val coroutineContext: CoroutineContext = dispatchers.main + SupervisorJob()

    init {
        activityRetainedLifecycle.addOnClearedListener {
            coroutineContext.cancel()
        }
    }
}
