package com.stt.android.common.viewstate

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import javax.inject.Inject

/**
 * For use with <PERSON><PERSON> and @AndroidEntryPoint annotated Fragments
 *
 * A base Fragment class for showing lists that extends [ViewStateFragment]
 * and manages a [RecyclerView] via [ViewStateEpoxyController].
 *
 * It implements [onStateChanged] and passes the state directly to [ViewStateEpoxyController.setData] for
 * building the Epoxy models.
 *
 * Aside from implementing the properties [layoutId] and [viewModel], you will also need to extend
 * [ViewStateEpoxyController] and override [ViewStateEpoxyController.buildModels] to setup the models (list items)
 * that will be shown in the RecyclerView.
 *
 * _Note that the provided layout resource must have a RecyclerView with ID `list`._ It is also recommended that
 * you specify the RecyclerView LayoutManager in the XML attributes.
 *
 * @param ViewStateData The data type that will be wrapped inside [ViewState] which
 * is emitted by all the _notify_ methods in [LoadingStateViewModel] such as [LoadingStateViewModel.notifyDataLoaded]
 * @param ViewModel The type of view model
 *
 * @see [ViewStateFragment]
 * @see [ViewStateEpoxyController]
 * @see [ViewState]
 */
// Note: derived classes should use @AndroidEntryPoint annotation
abstract class ViewStateBottomSheetListFragment<
    ViewStateData,
    ViewModel : LoadingStateViewModel<ViewStateData>,
    > : ViewStateBottomSheetFragment2<ViewStateData, ViewModel>() {

    @Inject
    internal lateinit var controller: ViewStateEpoxyController<ViewStateData?>

    protected val recyclerView: RecyclerView
        get() = _recyclerView ?: throw IllegalStateException(
            "Attempted to access recyclerView prior to view created/after view was destroyed"
        )
    private var _recyclerView: RecyclerView? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)

        _recyclerView = requireBinding<ViewDataBinding>().root.findViewById<RecyclerView>(R.id.list).apply {
            setHasFixedSize(true)
            adapter = controller.adapter
        }

        return view
    }

    @CallSuper
    override fun onDestroyView() {
        super.onDestroyView()
        _recyclerView = null
    }

    override fun onStateChanged(state: ViewState<ViewStateData?>) {
        controller.setData(state)
    }
}
