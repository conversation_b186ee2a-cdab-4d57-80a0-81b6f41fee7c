package com.stt.android.common.viewstate

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.snackbar.Snackbar
import com.stt.android.common.ui.ErrorEvent

/**
 * For use with Hilt and @AndroidEntryPoint annotated Fragments
 *
 * A base DialogFragment class that implements the ViewState pattern using [LoadingStateViewModel].
 *
 * It observes [LoadingStateViewModel.viewState] and dispatches relevant events. You need to implement [onStateChanged]
 * to be notified of the state event changes. Note that error events are handled and shown in a [Snackbar]
 * depending on the passed [ErrorEvent].
 *
 * The extending class must implement [viewModelClass] to provide the type of the ViewModel associated with this fragment
 * and implement [layoutId] to provide the layout resource ID that will be used in this fragment.
 *
 * @param ViewStateData The data type that will be wrapped inside [ViewState] which
 * is emitted by all the _notify_ methods in [LoadingStateViewModel] such as [LoadingStateViewModel.notifyDataLoaded]
 * @param ViewModel The type of view model
 *
 * @see [LoadingStateViewModel]
 * @see [ViewState]
 */
abstract class ViewStateDialogFragment2<
    ViewStateData,
    ViewModel : LoadingStateViewModel<ViewStateData>,
    > : DialogFragment(), ViewStateView<ViewStateData, ViewModel> {

    override lateinit var lifecycleOwner: LifecycleOwner

    private val viewStateDelegate: ViewStateViewDelegate<ViewStateData, ViewModel> by lazy {
        ViewStateViewDelegate(this)
    }

    protected fun <T : ViewDataBinding> requireBinding() = viewStateDelegate.requireBinding<T>()

    @CallSuper
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        lifecycleOwner = viewLifecycleOwner
        return viewStateDelegate.onCreateView(inflater, container, savedInstanceState)
    }

    @CallSuper
    override fun onDestroyView() {
        super.onDestroyView()
        viewStateDelegate.onDestroyView()
    }

    /**
     * The [ViewModel] that is associated to this Fragment
     */
    override val viewModel: ViewModel by lazy(LazyThreadSafetyMode.NONE) {
        ViewModelProvider(this).get(viewModelClass)
    }
}
