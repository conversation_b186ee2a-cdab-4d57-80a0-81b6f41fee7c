package com.stt.android.common.viewstate

import androidx.annotation.LayoutRes
import androidx.lifecycle.LifecycleOwner

/**
 * An interface that defines a view that supports a [ViewState].
 *
 * You'd normally want to use [ViewStateFragment] directly, which implements this interface. If that's not possible,
 * you'd need to implement this interface in the Fragment and pass the following calls to an instance of
 * [ViewStateViewDelegate]:
 *
 * 1. Fragment.onCreate() -> [ViewStateViewDelegate.onCreate]
 * 2. Fragment.onCreateView() -> [ViewStateViewDelegate.onCreateView]
 * 3. [binding] -> [ViewStateViewDelegate.binding]
 * 4. Fragment.onDestroyView() -> [ViewStateViewDelegate.onDestroyView]
 *
 * @see [ViewStateFragment]
 * @see [LoadingStateViewModel]
 * @see [ViewState]
 */
interface ViewStateView<
    ViewStateData,
    ViewModel : LoadingStateViewModel<ViewStateData>
    > {

    /**
     * Override this property and return the type of the ViewModel that is associated to this fragment
     */
    val viewModelClass: Class<ViewModel>

    /**
     * Override this property and return the layout resource ID that will be used in this fragment
     */
    @get:LayoutRes
    val layoutId: Int

    /**
     * Override this property [ViewModel]. For example:
     *
     * ```
     *     override val viewModel by viewModels { viewModelFactory }
     * ```
     *
     * NOTE: [viewModel] will be available only after [ViewStateViewDelegate.onCreate] is called!
     */
    val viewModel: ViewModel

    /**
     * Override this property and initialize in start of onCreateView. For example:
     *
     * ```
     *        override lateinit var lifecycleOwner: LifecycleOwner
     * ```
     *-
     * NOTE: Remember to initialize this using fragment's getViewLifecycleOwner() method
     *
     */
    val lifecycleOwner: LifecycleOwner

    /**
     * Event called when the view state has changed
     * @param state instance of [ViewState<ViewStateData>]
     */
    fun onStateChanged(state: ViewState<ViewStateData?>)
}
