package com.stt.android.common.viewstate

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.databinding.library.baseAdapters.BR
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.DataBindingHolder
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.observeNotNull
import timber.log.Timber

/**
 * View delegate that contains common code that is shared among different types of Fragments
 * and support [ViewState] handling. Use this delegate if you're implementing [ViewStateView] and
 * pass method calls to this delegate.
 *
 * @see [ViewStateView]
 */
class ViewStateViewDelegate<ViewStateData, ViewModel : LoadingStateViewModel<ViewStateData>>
constructor(
    private val viewStateView: ViewStateView<ViewStateData, ViewModel>
) {
    private lateinit var snackbar: Snackbar

    private val bindingHolder: DataBindingHolder = DataBindingHolder()

    private val binding get() = bindingHolder.binding!!

    @Suppress("UNCHECKED_CAST")
    fun <T : ViewDataBinding> requireBinding() = if (isBindingAvailable) {
        bindingHolder.binding as T
    } else {
        throw IllegalStateException("Binding not available")
    }

    val isBindingAvailable: Boolean
        get() = bindingHolder.binding != null

    fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        @Suppress("UNUSED_PARAMETER") savedInstanceState: Bundle?
    ): View? {
        viewStateView.viewModel.viewState.observeNotNull(viewStateView.lifecycleOwner) { state ->
            if (state is ViewState.Error) {
                showErrorSnackbar(state.errorEvent)
            } else {
                dismissErrorIfShown()
            }
            viewStateView.onStateChanged(state)
        }

        val binding = bindingHolder.inflate(
            inflater,
            viewStateView.layoutId,
            container,
            false
        ).apply {
            lifecycleOwner = viewStateView.lifecycleOwner
            setVariable(BR.viewModel, viewStateView.viewModel)
        }
        return binding.root
    }

    fun onDestroyView() {
        bindingHolder.clear()
    }

    private fun showErrorSnackbar(errorEvent: ErrorEvent) {
        if (isBindingAvailable) {
            snackbar = Snackbar.make(
                binding.root,
                errorEvent.errorStringRes,
                if (errorEvent.showCloseButton || errorEvent.canRetry) {
                    Snackbar.LENGTH_INDEFINITE
                } else {
                    Snackbar.LENGTH_LONG
                }
            )
        }
        if (errorEvent.canRetry) {
            snackbar.setAction(R.string.retry_action) {
                Timber.d("Retry button clicked")
                viewStateView.viewModel.retryLoading()
            }
        } else {
            if (errorEvent.showCloseButton) {
                snackbar.setAction(R.string.close) {}
            }
        }
        snackbar.show()
    }

    private fun dismissErrorIfShown() {
        if (this::snackbar.isInitialized) {
            snackbar.dismiss()
        }
    }
}
