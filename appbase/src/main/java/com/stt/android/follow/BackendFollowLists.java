package com.stt.android.follow;

import com.google.gson.annotations.SerializedName;
import java.util.ArrayList;
import java.util.List;

public class BackendFollowLists {

    @SerializedName("followers")
    private List<BackendFollowStatusChangeWithAdditionalInformation> followers;
    @SerializedName("followings")
    private List<BackendFollowStatusChangeWithAdditionalInformation> followings;

    public BackendFollowLists(List<BackendFollowStatusChangeWithAdditionalInformation> followers,
        List<BackendFollowStatusChangeWithAdditionalInformation> followings) {
        this.followers = followers;
        this.followings = followings;
    }

    public FollowLists toFollowLists() {
        List<UserFollowStatus> localFollowers = new ArrayList<>();
        List<UserFollowStatus> localFollowings = new ArrayList<>();
        if (followers != null) {
            for (BackendFollowStatusChangeWithAdditionalInformation follower : followers) {
                localFollowers.add(follower.toUserFollowStatus(FollowDirection.FOLLOWER));
            }
        }
        if (followings != null) {
            for (BackendFollowStatusChangeWithAdditionalInformation following : followings) {
                localFollowings.add(following.toUserFollowStatus(FollowDirection.FOLLOWING));
            }
        }
        return new FollowLists(localFollowers, localFollowings);
    }

}
