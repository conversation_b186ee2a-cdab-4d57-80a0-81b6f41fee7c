package com.stt.android.follow;

public enum FollowStatus {
    /**
     * Following or being followed (depends on direction)
     */
    FOLLOWING,
    /**
     * Either pending accept request by other user or current user has to approve it
     */
    PENDING,
    /**
     * The other user didn't accept our request
     */
    REJECTED, // rejected e.g. due to block
    /**
     * When it's not following
     */
    UNFOLLOWING,
    /**
     * Backend failed to change status
     */
    FAILED,
    /**
     * Backend following each other
     */
    FRIENDS,
}
