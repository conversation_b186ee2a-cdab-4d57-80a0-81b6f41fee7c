package com.stt.android.follow;

import com.google.gson.annotations.SerializedName;

public class BackendFollowStatusChangeWithAdditionalInformation {
    @SerializedName("username")
    private String username;
    @SerializedName("status")
    private FollowStatus status;
    @SerializedName("realName")
    private String realName;
    @SerializedName("profileDescription")
    private String profileDescription;
    @SerializedName("profileImageUrl")
    private String profileImageUrl;
    @SerializedName("coverImageUrl")
    private String coverImageUrl;

    public BackendFollowStatusChangeWithAdditionalInformation(String username,
        FollowStatus status, String realName, String profileDescription,
        String profileImageUrl, String coverImageUrl) {
        this.username = username;
        this.status = status;
        this.realName = realName;
        this.profileDescription = profileDescription;
        this.profileImageUrl = profileImageUrl;
        this.coverImageUrl = coverImageUrl;
    }

    public UserFollowStatus toUserFollowStatus(FollowDirection direction) {
        return new UserFollowStatus(username, status, realName, profileDescription,
            profileImageUrl, coverImageUrl, direction, null, false);
    }
}
