package com.stt.android.follow;

import androidx.annotation.NonNull;
import com.google.gson.annotations.SerializedName;

public class BackendFollowStatusChange {
    @SerializedName("username")
    private String username;
    @SerializedName("status")
    private FollowStatus status;

    public BackendFollowStatusChange(String username, FollowStatus status) {
        this.username = username;
        this.status = status;
    }

    public UserFollowStatus toUserFollowStatus(@NonNull FollowDirection direction) {
        return new UserFollowStatus.Builder(direction).setUsername(username)
            .setStatus(status)
            .build();
    }

    /**
     * @return the new status for this {@link #username}
     */
    public FollowStatus getStatus() {
        return status;
    }

    public String getUsername() {
        return username;
    }
}
