package com.stt.android.follow;

import java.util.List;

public class FollowLists {

    private List<UserFollowStatus> followers;
    private List<UserFollowStatus> followings;

    public FollowLists(List<UserFollowStatus> followers, List<UserFollowStatus> followings) {
        this.followers = followers;
        this.followings = followings;
    }

    public List<UserFollowStatus> getFollowers() {
        return followers;
    }

    public List<UserFollowStatus> getFollowings() {
        return followings;
    }
}
